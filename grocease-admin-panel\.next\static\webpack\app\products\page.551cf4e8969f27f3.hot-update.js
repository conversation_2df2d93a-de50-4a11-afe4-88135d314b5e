"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/image-upload */ \"(app-pages-browser)/./src/components/ui/image-upload.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_18__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [featuredFilter, setFeaturedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        image: \"\",\n        categoryId: 0,\n        unit: \"\",\n        inStock: true,\n        rating: 0,\n        reviewCount: 0,\n        tags: [],\n        isFeatured: false,\n        stockQuantity: 0\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient)();\n    const { data: productsData, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery)({\n        queryKey: [\n            \"products\",\n            page,\n            search,\n            categoryFilter,\n            featuredFilter\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].getProducts(page, 10, categoryFilter === \"ALL\" ? undefined : categoryFilter, search || undefined)\n    });\n    const { data: categories } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery)({\n        queryKey: [\n            \"categories\"\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].getCategories()\n    });\n    const createMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (data)=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].createProduct(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsCreateDialogOpen(false);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product created successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to create product\");\n        }\n    });\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].updateProduct(id, data);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsEditDialogOpen(false);\n            setEditingProduct(null);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product updated successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update product\");\n        }\n    });\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation)({\n        mutationFn: (id)=>_lib_api__WEBPACK_IMPORTED_MODULE_14__[\"default\"].deleteProduct(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Product deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete product\");\n        }\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            image: \"\",\n            categoryId: 0,\n            unit: \"\",\n            inStock: true,\n            rating: 0,\n            reviewCount: 0,\n            tags: [],\n            isFeatured: false,\n            stockQuantity: 0\n        });\n    };\n    const handleCreate = ()=>{\n        createMutation.mutate(formData);\n    };\n    const handleEdit = (product)=>{\n        var _categories_find;\n        setEditingProduct(product);\n        // Find the category ID from the category name\n        const categoryId = (categories === null || categories === void 0 ? void 0 : (_categories_find = categories.find((cat)=>cat.name === product.category)) === null || _categories_find === void 0 ? void 0 : _categories_find.id) || \"0\";\n        setFormData({\n            name: product.name,\n            description: product.description,\n            price: product.price,\n            originalPrice: product.originalPrice || 0,\n            discount: product.discount || 0,\n            image: product.image,\n            categoryId: parseInt(categoryId),\n            unit: product.unit,\n            inStock: product.inStock,\n            rating: product.rating,\n            reviewCount: product.reviewCount,\n            tags: product.tags || [],\n            isFeatured: product.isFeatured || false,\n            stockQuantity: product.stockQuantity || 0\n        });\n        setIsEditDialogOpen(true);\n    };\n    const handleUpdate = ()=>{\n        if (editingProduct) {\n            updateMutation.mutate({\n                id: editingProduct.id,\n                data: formData\n            });\n        }\n    };\n    const handleDelete = (id)=>{\n        if (confirm(\"Are you sure you want to delete this product?\")) {\n            deleteMutation.mutate(id);\n        }\n    };\n    const filteredProducts = ((productsData === null || productsData === void 0 ? void 0 : productsData.data) || []).filter((product)=>{\n        if (featuredFilter === \"FEATURED\") return product.isFeatured;\n        if (featuredFilter === \"REGULAR\") return !product.isFeatured;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Manage your product catalog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                            open: isCreateDialogOpen,\n                            onOpenChange: setIsCreateDialogOpen,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resetForm,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Product\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Filter Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Search and filter products by category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    placeholder: \"Search products...\",\n                                                    value: search,\n                                                    onChange: (e)=>setSearch(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Filter by category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"ALL\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                            value: category.id,\n                                                            children: category.name\n                                                        }, category.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: featuredFilter,\n                                        onValueChange: setFeaturedFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-40\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Featured\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"ALL\",\n                                                        children: \"All Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"FEATURED\",\n                                                        children: \"Featured Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"REGULAR\",\n                                                        children: \"Regular Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Products List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        (productsData === null || productsData === void 0 ? void 0 : productsData.pagination.total) || 0,\n                                        \" total products\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_17___default()), {\n                                                                                src: product.image,\n                                                                                alt: product.name,\n                                                                                width: 50,\n                                                                                height: 50,\n                                                                                className: \"rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 313,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: product.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: product.unit\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: product.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground line-through\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatCurrency)(product.originalPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium \".concat(!product.inStock ? \"text-red-600\" : \"\"),\n                                                                        children: product.inStock ? \"In Stock\" : \"Out of Stock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                \"(\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.formatNumber)(product.reviewCount),\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: product.isFeatured ? \"default\" : \"outline\",\n                                                                    children: product.isFeatured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"h-3 w-3 fill-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Featured\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 31\n                                                                    }, this) : \"Regular\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: product.inStock ? \"default\" : \"destructive\",\n                                                                    children: product.inStock ? \"Active\" : \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_18___default()), {\n                                                                            href: \"/products/\".concat(product.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 380,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 379,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 378,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleEdit(product),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            disabled: deleteMutation.isPending,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, product.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this),\n                                    (productsData === null || productsData === void 0 ? void 0 : productsData.pagination) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Showing \",\n                                                    page * 10 + 1,\n                                                    \" to \",\n                                                    Math.min((page + 1) * 10, productsData.pagination.total),\n                                                    \" of\",\n                                                    \" \",\n                                                    productsData.pagination.total,\n                                                    \" products\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page - 1),\n                                                        disabled: page === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Previous\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page + 1),\n                                                        disabled: page >= productsData.pagination.totalPages - 1,\n                                                        children: [\n                                                            \"Next\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isCreateDialogOpen,\n                    onOpenChange: setIsCreateDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Create Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Add a new product to your catalog.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"discount\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"stockQuantity\",\n                                                children: \"Stock Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"stockQuantity\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                value: formData.stockQuantity || 0,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        stockQuantity: parseInt(e.target.value) || 0\n                                                    }),\n                                                placeholder: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"isFeatured\",\n                                                checked: formData.isFeatured || false,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        isFeatured: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"isFeatured\",\n                                                children: \"Featured Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleCreate,\n                                    disabled: createMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: createMutation.isPending ? \"Creating...\" : \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 598,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isEditDialogOpen,\n                    onOpenChange: setIsEditDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Edit Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Update the product information.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"edit-name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"edit-description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-price\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-discount\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-stockQuantity\",\n                                                children: \"Stock Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"edit-stockQuantity\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                value: formData.stockQuantity || 0,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        stockQuantity: parseInt(e.target.value) || 0\n                                                    }),\n                                                placeholder: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"edit-inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"edit-isFeatured\",\n                                                checked: formData.isFeatured || false,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        isFeatured: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-isFeatured\",\n                                                children: \"Featured Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleUpdate,\n                                    disabled: updateMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: updateMutation.isPending ? \"Updating...\" : \"Update Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 611,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"EQj7GUzZKdDrBQ9ZEQ28sSMBh3o=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useMutation\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});