import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Order } from '../types';
import { formatDate, formatPrice } from '../utils';
import Button from '../components/Button';
import LoadingSpinner from '../components/LoadingSpinner';

type OrderTrackingRouteProp = RouteProp<{
  OrderTracking: { orderId: string };
}, 'OrderTracking'>;

const OrderTrackingScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<OrderTrackingRouteProp>();
  const { orderId } = route.params;

  // Fetch order details
  const { 
    data: orderData, 
    isLoading, 
    refetch,
    error 
  } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => api.getOrder(orderId),
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });

  const order = orderData?.data;

  const getStatusSteps = () => {
    const steps = [
      { key: 'CONFIRMED', label: 'Order Confirmed', icon: 'checkmark-circle' },
      { key: 'PREPARING', label: 'Preparing', icon: 'restaurant' },
      { key: 'OUT_FOR_DELIVERY', label: 'Out for Delivery', icon: 'car' },
      { key: 'DELIVERED', label: 'Delivered', icon: 'home' },
    ];

    if (order?.status === 'CANCELLED') {
      return [
        { key: 'CONFIRMED', label: 'Order Confirmed', icon: 'checkmark-circle' },
        { key: 'CANCELLED', label: 'Cancelled', icon: 'close-circle' },
      ];
    }

    return steps;
  };

  const getCurrentStepIndex = () => {
    if (!order) return 0;

    const statusMap: { [key: string]: number } = {
      'PENDING': 0,
      'CONFIRMED': 0,
      'PREPARING': 1,
      'OUT_FOR_DELIVERY': 2,
      'DELIVERED': 3,
      'CANCELLED': 1,
    };

    return statusMap[order.status] || 0;
  };

  const getStepStatus = (stepIndex: number) => {
    const currentIndex = getCurrentStepIndex();

    if (order?.status === 'CANCELLED') {
      if (stepIndex === 0) return 'completed';
      if (stepIndex === 1) return 'cancelled';
      return 'pending';
    }

    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'current':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'cancelled':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-neutral-400 bg-neutral-50 border-neutral-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return 'checkmark-circle';
      case 'current':
        return 'time';
      case 'cancelled':
        return 'close-circle';
      default:
        return 'ellipse-outline';
    }
  };

  const getEstimatedTime = () => {
    if (!order) return '';

    switch (order.status) {
      case 'CONFIRMED':
        return 'Preparing your order...';
      case 'PREPARING':
        return 'Estimated ready in 15-20 minutes';
      case 'OUT_FOR_DELIVERY':
        return 'Arriving in 10-15 minutes';
      case 'DELIVERED':
        return `Delivered on ${formatDate(order.orderDate)}`;
      case 'CANCELLED':
        return 'Order was cancelled';
      default:
        return '';
    }
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading order tracking..." fullScreen />;
  }

  if (error || !order) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="alert-circle" size={80} color="#ef4444" />
          <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
            Order Not Found
          </Text>
          <Text className="text-base text-neutral-600 text-center mb-8">
            We couldn't find the order you're looking for.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  const steps = getStatusSteps();

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white border-b border-neutral-200 px-4 py-4">
        <View className="flex-row items-center">
          <Button
            onPress={() => navigation.goBack()}
            variant="ghost"
            size="sm"
            className="mr-3"
          >
            <Ionicons name="arrow-back" size={24} color="#1e293b" />
          </Button>
          <View className="flex-1">
            <Text className="text-lg font-bold text-neutral-800">
              Track Order
            </Text>
            <Text className="text-sm text-neutral-600">
              Order #{order.id.slice(-8).toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      <ScrollView 
        className="flex-1" 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={refetch} />
        }
      >
        <View className="px-4 py-6">
          {/* Current Status */}
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-neutral-100">
            <View className="items-center mb-4">
              <View className={`w-16 h-16 rounded-full items-center justify-center mb-3 ${
                order.status === 'DELIVERED' ? 'bg-green-100' :
                order.status === 'CANCELLED' ? 'bg-red-100' : 'bg-blue-100'
              }`}>
                <Ionicons
                  name={
                    order.status === 'DELIVERED' ? 'checkmark-circle' :
                    order.status === 'CANCELLED' ? 'close-circle' : 'time'
                  }
                  size={32}
                  color={
                    order.status === 'DELIVERED' ? '#16a34a' :
                    order.status === 'CANCELLED' ? '#dc2626' : '#2563eb'
                  }
                />
              </View>
              <Text className={`text-xl font-bold mb-1 ${
                order.status === 'DELIVERED' ? 'text-green-600' :
                order.status === 'CANCELLED' ? 'text-red-600' : 'text-blue-600'
              }`}>
                {order.status === 'DELIVERED' ? 'Order Delivered!' :
                 order.status === 'CANCELLED' ? 'Order Cancelled' :
                 order.status === 'OUT_FOR_DELIVERY' ? 'Out for Delivery' :
                 order.status === 'PREPARING' ? 'Preparing Your Order' :
                 'Order Confirmed'}
              </Text>
              <Text className="text-sm text-neutral-600 text-center">
                {getEstimatedTime()}
              </Text>
            </View>
          </View>

          {/* Tracking Steps */}
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-neutral-100">
            <Text className="text-lg font-bold text-neutral-800 mb-4">
              Order Progress
            </Text>
            
            <View className="space-y-4">
              {steps.map((step, index) => {
                const stepStatus = getStepStatus(index);
                const isLast = index === steps.length - 1;
                
                return (
                  <View key={step.key} className="flex-row items-start">
                    {/* Step Icon */}
                    <View className="items-center mr-4">
                      <View className={`w-10 h-10 rounded-full items-center justify-center border-2 ${getStatusColor(stepStatus)}`}>
                        <Ionicons 
                          name={getStatusIcon(stepStatus) as any} 
                          size={20} 
                          color={
                            stepStatus === 'completed' ? '#16a34a' :
                            stepStatus === 'current' ? '#2563eb' :
                            stepStatus === 'cancelled' ? '#dc2626' : '#9ca3af'
                          } 
                        />
                      </View>
                      {!isLast && (
                        <View className={`w-0.5 h-8 mt-2 ${
                          stepStatus === 'completed' ? 'bg-green-300' : 'bg-neutral-200'
                        }`} />
                      )}
                    </View>
                    
                    {/* Step Content */}
                    <View className="flex-1 pb-2">
                      <Text className={`font-semibold ${
                        stepStatus === 'completed' ? 'text-green-600' :
                        stepStatus === 'current' ? 'text-blue-600' :
                        stepStatus === 'cancelled' ? 'text-red-600' : 'text-neutral-400'
                      }`}>
                        {step.label}
                      </Text>
                      {stepStatus === 'current' && (
                        <Text className="text-sm text-neutral-600 mt-1">
                          In progress...
                        </Text>
                      )}
                      {stepStatus === 'completed' && step.key === 'delivered' && (
                        <Text className="text-sm text-neutral-600 mt-1">
                          {formatDate(order.orderDate)}
                        </Text>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          </View>

          {/* Order Summary */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100">
            <Text className="text-lg font-bold text-neutral-800 mb-4">
              Order Summary
            </Text>
            
            <View className="space-y-3">
              <View className="flex-row justify-between">
                <Text className="text-neutral-600">Order Total</Text>
                <Text className="font-semibold text-neutral-800">
                  {formatPrice(order.total)}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-neutral-600">Items</Text>
                <Text className="font-semibold text-neutral-800">
                  {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-neutral-600">Order Date</Text>
                <Text className="font-semibold text-neutral-800">
                  {formatDate(order.orderDate)}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View className="bg-white border-t border-neutral-200 px-4 py-4">
        <View className="space-y-3">
          <Button
            title="View Order Details"
            onPress={() => {
              navigation.navigate('OrderDetails' as never, {
                orderId: order.id
              } as never);
            }}
            variant="outline"
            size="lg"
            fullWidth
          />
          <Button
            title="Continue Shopping"
            onPress={() => navigation.navigate('Main' as never)}
            size="lg"
            fullWidth
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OrderTrackingScreen;
