package com.grocease.service;

import com.cloudinary.Cloudinary;
import com.cloudinary.utils.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

@Service
@Slf4j
public class CloudinaryService {

    private final Cloudinary cloudinary;

    public CloudinaryService(@Autowired(required = false) Cloudinary cloudinary) {
        this.cloudinary = cloudinary;
    }

    public String uploadImage(MultipartFile file, String folder) throws IOException {
        if (cloudinary == null) {
            log.warn("Cloudinary not configured. Returning placeholder URL for file: {}",
                    file != null ? file.getOriginalFilename() : "unknown");
            return "https://via.placeholder.com/400x400?text=Image";
        }

        // Validate file before processing
        if (file == null || file.isEmpty()) {
            throw new IOException("File is null or empty");
        }

        // Validate filename
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IOException("File must have a valid filename");
        }

        // Check file size to prevent buffer overflow - be more defensive
        long fileSize;
        try {
            fileSize = file.getSize();
        } catch (Exception e) {
            log.error("Error getting file size: {}", e.getMessage());
            throw new IOException("Unable to determine file size: " + e.getMessage());
        }

        if (fileSize <= 0) {
            throw new IOException("Invalid file size: " + fileSize + " bytes");
        }

        // Additional size validation to prevent buffer issues
        if (fileSize > 50 * 1024 * 1024) { // 50MB hard limit
            throw new IOException("File too large: " + fileSize + " bytes (max 50MB)");
        }

        log.info("Uploading image to Cloudinary - folder: {}, filename: {}, size: {} bytes",
                folder, originalFilename, fileSize);

        Map<String, Object> uploadParams = ObjectUtils.asMap(
                "folder", folder,
                "resource_type", "image",
                "quality", "auto",
                "fetch_format", "auto"
        );

        try {
            // Convert MultipartFile to byte array for Cloudinary compatibility
            byte[] fileBytes;
            try {
                fileBytes = file.getBytes();
                if (fileBytes == null || fileBytes.length == 0) {
                    throw new IOException("File bytes are null or empty");
                }
            } catch (Exception e) {
                log.error("Error reading file bytes: {}", e.getMessage());
                throw new IOException("Unable to read file data: " + e.getMessage(), e);
            }

            // Upload using byte array which is more compatible with Cloudinary
            Map<String, Object> uploadResult = cloudinary.uploader().upload(fileBytes, uploadParams);

            if (uploadResult == null || !uploadResult.containsKey("secure_url")) {
                throw new IOException("Invalid response from Cloudinary - no secure URL returned");
            }

            String imageUrl = (String) uploadResult.get("secure_url");
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                throw new IOException("Empty image URL returned from Cloudinary");
            }

            log.info("Image uploaded successfully: {}", imageUrl);
            return imageUrl;
        } catch (IndexOutOfBoundsException e) {
            log.error("Buffer overflow error during image upload: {}", e.getMessage(), e);
            throw new IOException("File processing error - the file may be corrupted or have an invalid format", e);
        } catch (Exception e) {
            log.error("Error uploading image to Cloudinary: {}", e.getMessage(), e);
            throw new IOException("Failed to upload image: " + e.getMessage(), e);
        }
    }

    public String uploadUserAvatar(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/avatars");
    }

    public String uploadProductImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/products");
    }

    public String uploadCategoryImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/categories");
    }

    public String uploadBannerImage(MultipartFile file) throws IOException {
        return uploadImage(file, "grocease/banners");
    }

    public void deleteImage(String imageUrl) {
        if (cloudinary == null) {
            log.warn("Cloudinary not configured. Cannot delete image: {}", imageUrl);
            return;
        }

        try {
            // Extract public_id from URL
            String publicId = extractPublicIdFromUrl(imageUrl);
            if (publicId != null) {
                cloudinary.uploader().destroy(publicId, ObjectUtils.emptyMap());
                log.info("Image deleted successfully: {}", publicId);
            }
        } catch (Exception e) {
            log.error("Error deleting image: {}", imageUrl, e);
        }
    }

    private String extractPublicIdFromUrl(String imageUrl) {
        try {
            // Validate input
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                log.warn("Image URL is null or empty");
                return null;
            }

            // Extract public_id from Cloudinary URL
            // Example: https://res.cloudinary.com/demo/image/upload/v1234567890/grocease/products/sample.jpg
            String[] parts = imageUrl.split("/");

            // Ensure we have enough parts for the expected URL structure (minimum 8 parts for Cloudinary URL)
            if (parts.length < 8) {
                log.warn("URL does not have expected Cloudinary structure (too few parts: {}): {}", parts.length, imageUrl);
                return null;
            }

            try {
                // Find the upload part to locate the version and folder structure
                int uploadIndex = -1;
                for (int i = 0; i < parts.length; i++) {
                    if ("upload".equals(parts[i])) {
                        uploadIndex = i;
                        break;
                    }
                }

                if (uploadIndex == -1 || uploadIndex + 3 >= parts.length) {
                    log.warn("URL does not contain 'upload' segment or insufficient parts after upload: {}", imageUrl);
                    return null;
                }

                // Extract parts after version (skip version part like v1234567890)
                StringBuilder publicId = new StringBuilder();
                for (int i = uploadIndex + 2; i < parts.length; i++) { // Skip upload and version
                    if (i > uploadIndex + 2) {
                        publicId.append("/");
                    }

                    String part = parts[i];
                    // For the last part (filename), remove extension
                    if (i == parts.length - 1) {
                        int lastDotIndex = part.lastIndexOf('.');
                        if (lastDotIndex > 0) {
                            part = part.substring(0, lastDotIndex);
                        }
                    }
                    publicId.append(part);
                }

                String result = publicId.toString();
                if (result.isEmpty()) {
                    log.warn("Extracted public_id is empty for URL: {}", imageUrl);
                    return null;
                }

                log.debug("Extracted public_id: {} from URL: {}", result, imageUrl);
                return result;

            } catch (IndexOutOfBoundsException e) {
                log.error("Index out of bounds while parsing URL parts: {}", imageUrl, e);
                return null;
            }

        } catch (Exception e) {
            log.error("Error extracting public_id from URL: {}", imageUrl, e);
        }
        return null;
    }
}
