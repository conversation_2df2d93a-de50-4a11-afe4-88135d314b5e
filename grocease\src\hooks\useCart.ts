import { create } from 'zustand';
import { CartItem, Product } from '../types';
import { calculateCartTotal } from '../utils';

interface CartStore {
  items: CartItem[];
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getCartSummary: () => ReturnType<typeof calculateCartTotal>;
}

export const useCart = create<CartStore>((set, get) => ({
  items: [],
  
  addItem: (product: Product, quantity = 1) => {
    set((state) => {
      // Check if product is in stock
      if (!product.inStock) {
        throw new Error(`${product.name} is out of stock`);
      }

      const existingItem = state.items.find(item => item.product.id === product.id);
      const currentCartQuantity = existingItem?.quantity || 0;
      const newTotalQuantity = currentCartQuantity + quantity;

      // Check stock quantity if it's defined (null means unlimited stock)
      if (product.stockQuantity !== null && product.stockQuantity !== undefined) {
        if (product.stockQuantity === 0) {
          throw new Error(`${product.name} is out of stock`);
        }
        if (newTotalQuantity > product.stockQuantity) {
          const availableQuantity = product.stockQuantity - currentCartQuantity;
          if (availableQuantity <= 0) {
            throw new Error(`${product.name} is out of stock`);
          }
          throw new Error(`Only ${availableQuantity} ${product.name} available in stock`);
        }
      }

      if (existingItem) {
        return {
          items: state.items.map(item =>
            item.product.id === product.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          )
        };
      }

      return {
        items: [...state.items, { product, quantity }]
      };
    });
  },
  
  removeItem: (productId: string) => {
    set((state) => ({
      items: state.items.filter(item => item.product.id !== productId)
    }));
  },
  
  updateQuantity: (productId: string, quantity: number) => {
    if (quantity <= 0) {
      get().removeItem(productId);
      return;
    }

    set((state) => {
      const item = state.items.find(item => item.product.id === productId);
      if (!item) return state;

      const product = item.product;

      // Check if product is in stock
      if (!product.inStock) {
        throw new Error(`${product.name} is out of stock`);
      }

      // Check stock quantity if it's defined (null means unlimited stock)
      if (product.stockQuantity !== null && product.stockQuantity !== undefined) {
        if (product.stockQuantity === 0) {
          throw new Error(`${product.name} is out of stock`);
        }
        if (quantity > product.stockQuantity) {
          throw new Error(`Only ${product.stockQuantity} ${product.name} available in stock`);
        }
      }

      return {
        items: state.items.map(item =>
          item.product.id === productId
            ? { ...item, quantity }
            : item
        )
      };
    });
  },
  
  clearCart: () => {
    set({ items: [] });
  },
  
  getCartSummary: () => {
    return calculateCartTotal(get().items);
  }
}));
