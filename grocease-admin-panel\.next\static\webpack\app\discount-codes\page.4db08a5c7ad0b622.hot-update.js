"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/discount-codes/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nclass ApiClient {\n    // Auth endpoints\n    async login(credentials) {\n        const response = await this.client.post(\"/auth/login\", credentials);\n        return response.data.data;\n    }\n    // Dashboard endpoints\n    async getDashboardOverview() {\n        const response = await this.client.get(\"/admin/dashboard/overview\");\n        return response.data.data;\n    }\n    // Analytics endpoints\n    async getMonthlySalesData() {\n        let months = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 12;\n        const response = await this.client.get(\"/analytics/sales/monthly?months=\".concat(months));\n        return response.data.data;\n    }\n    async getWeeklySalesData() {\n        let weeks = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 12;\n        const response = await this.client.get(\"/analytics/sales/weekly?weeks=\".concat(weeks));\n        return response.data.data;\n    }\n    async getMostSoldProducts() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        const response = await this.client.get(\"/analytics/products/most-sold?days=\".concat(days, \"&limit=\").concat(limit));\n        return response.data.data;\n    }\n    async getPopularCategories() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await this.client.get(\"/analytics/categories/popular?days=\".concat(days));\n        return response.data.data;\n    }\n    async getUserEngagementMetrics() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await this.client.get(\"/analytics/users/engagement?days=\".concat(days));\n        return response.data.data;\n    }\n    // Order endpoints\n    async getOrders() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0, status = arguments.length > 3 ? arguments[3] : void 0;\n        // Convert 0-based page to 1-based for backend\n        const backendPage = Math.max(1, page + 1);\n        const params = new URLSearchParams({\n            page: backendPage.toString(),\n            limit: limit.toString()\n        });\n        if (search && search.trim()) {\n            params.append(\"search\", search.trim());\n        }\n        if (status && status !== \"ALL\") {\n            params.append(\"status\", status);\n        }\n        const response = await this.client.get(\"/admin/orders?\".concat(params));\n        return response.data;\n    }\n    async getOrder(id) {\n        const response = await this.client.get(\"/admin/orders/\".concat(id));\n        return response.data.data;\n    }\n    async updateOrderStatus(id, status, notes) {\n        const response = await this.client.put(\"/admin/orders/\".concat(id, \"/status\"), {\n            status,\n            notes\n        });\n        return response.data.data;\n    }\n    // User endpoints\n    async getUsers() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        const response = await this.client.get(\"/admin/users?page=\".concat(page, \"&limit=\").concat(limit));\n        return response.data;\n    }\n    async getUser(id) {\n        const response = await this.client.get(\"/admin/users/\".concat(id));\n        return response.data.data;\n    }\n    async updateProfile(profileData) {\n        const response = await this.client.put(\"/users/profile\", profileData);\n        return response.data.data;\n    }\n    // Product endpoints\n    async getProducts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, categoryId = arguments.length > 2 ? arguments[2] : void 0, search = arguments.length > 3 ? arguments[3] : void 0;\n        let url = \"/products?page=\".concat(page, \"&limit=\").concat(limit);\n        if (categoryId) url += \"&categoryId=\".concat(categoryId);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async getProduct(id) {\n        const response = await this.client.get(\"/products/\".concat(id));\n        return response.data.data;\n    }\n    async getCategories() {\n        const response = await this.client.get(\"/categories\");\n        return response.data.data;\n    }\n    async getBanners() {\n        const response = await this.client.get(\"/banners\");\n        return response.data.data;\n    }\n    async getFeaturedProducts() {\n        const response = await this.client.get(\"/products/featured\");\n        return response.data.data;\n    }\n    // Notification endpoints\n    async sendNotification(request) {\n        await this.client.post(\"/admin/notifications/send\", request);\n    }\n    async getNotificationHistory() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, userId = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/notifications/history?page=\".concat(page, \"&size=\").concat(size);\n        if (userId) url += \"&userId=\".concat(userId);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    // File upload endpoints\n    async uploadImage(file, type) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const response = await this.client.post(\"/upload/\".concat(type), formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        return response.data.data;\n    }\n    // Admin Product Management\n    async createProduct(productData) {\n        const response = await this.client.post(\"/admin/products\", productData);\n        return response.data.data;\n    }\n    async updateProduct(id, productData) {\n        const response = await this.client.put(\"/admin/products/\".concat(id), productData);\n        return response.data.data;\n    }\n    async deleteProduct(id) {\n        await this.client.delete(\"/admin/products/\".concat(id));\n    }\n    // Admin Category Management\n    async getAdminCategories() {\n        const response = await this.client.get(\"/admin/products/categories\");\n        return response.data.data;\n    }\n    async createCategory(categoryData) {\n        const response = await this.client.post(\"/admin/products/categories\", categoryData);\n        return response.data.data;\n    }\n    async updateCategory(id, categoryData) {\n        const response = await this.client.put(\"/admin/products/categories/\".concat(id), categoryData);\n        return response.data.data;\n    }\n    async deleteCategory(id) {\n        await this.client.delete(\"/admin/products/categories/\".concat(id));\n    }\n    // Admin Banner Management\n    async getBanners() {\n        const response = await this.client.get(\"/admin/banners\");\n        return response.data.data;\n    }\n    async getBanner(id) {\n        const response = await this.client.get(\"/admin/banners/\".concat(id));\n        return response.data.data;\n    }\n    async createBanner(bannerData) {\n        const response = await this.client.post(\"/admin/banners\", bannerData);\n        return response.data.data;\n    }\n    async updateBanner(id, bannerData) {\n        const response = await this.client.put(\"/admin/banners/\".concat(id), bannerData);\n        return response.data.data;\n    }\n    async deleteBanner(id) {\n        await this.client.delete(\"/admin/banners/\".concat(id));\n    }\n    async toggleBannerStatus(id) {\n        const response = await this.client.put(\"/admin/banners/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    // Admin User Management\n    async getAdminUsers() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/users?page=\".concat(page, \"&limit=\").concat(limit);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async toggleUserStatus(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    async verifyUserEmail(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/verify-email\"));\n        return response.data.data;\n    }\n    async verifyUserPhone(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/verify-phone\"));\n        return response.data.data;\n    }\n    async deleteUser(id) {\n        await this.client.delete(\"/admin/users/\".concat(id));\n    }\n    // Admin Discount Code Management\n    async getDiscountCodes() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/discount-codes?page=\".concat(page, \"&limit=\").concat(limit);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async getDiscountCode(id) {\n        const response = await this.client.get(\"/admin/discount-codes/\".concat(id));\n        return response.data.data;\n    }\n    async createDiscountCode(discountCodeData) {\n        const response = await this.client.post(\"/admin/discount-codes\", discountCodeData);\n        return response.data.data;\n    }\n    async updateDiscountCode(id, discountCodeData) {\n        const response = await this.client.put(\"/admin/discount-codes/\".concat(id), discountCodeData);\n        return response.data.data;\n    }\n    async deleteDiscountCode(id) {\n        await this.client.delete(\"/admin/discount-codes/\".concat(id));\n    }\n    async toggleDiscountCodeStatus(id) {\n        const response = await this.client.post(\"/admin/discount-codes/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    async applyDiscountCode(request) {\n        const response = await this.client.post(\"/discount-codes/apply\", request);\n        return response.data.data;\n    }\n    async validateDiscountCode(code) {\n        const response = await this.client.get(\"/discount-codes/validate/\".concat(code));\n        return response.data.data;\n    }\n    // Admin Product Featured Management\n    async toggleProductFeaturedStatus(id) {\n        const response = await this.client.put(\"/admin/products/\".concat(id, \"/toggle-featured\"));\n        return response.data.data;\n    }\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: \"http://localhost:8080/api\" || 0,\n            timeout: 10000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = localStorage.getItem(\"admin_token\");\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                localStorage.removeItem(\"admin_token\");\n                localStorage.removeItem(\"admin_user\");\n                window.location.href = \"/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});