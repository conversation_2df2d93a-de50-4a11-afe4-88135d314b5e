import React from 'react';
import { TouchableOpacity, Text, View, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Product } from '../types';
import { formatPrice, calculateDiscount } from '../utils';

interface ProductCardProps {
  product: Product;
  onPress: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  onAddToCart
}) => {
  const discount = product.originalPrice
    ? calculateDiscount(product.originalPrice, product.price)
    : 0;

  // Check if product is available for purchase
  const isOutOfStock = !product.inStock || (product.stockQuantity !== null && product.stockQuantity !== undefined && product.stockQuantity === 0);
  const isLowStock = product.stockQuantity !== null && product.stockQuantity !== undefined && product.stockQuantity > 0 && product.stockQuantity <= 5;

  const handleAddToCart = () => {
    try {
      onAddToCart(product);
    } catch (error) {
      Alert.alert(
        'Cannot Add to Cart',
        error instanceof Error ? error.message : 'This product is not available',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <TouchableOpacity
      className="bg-white rounded-2xl shadow-sm border border-neutral-100 overflow-hidden"
      onPress={() => onPress(product)}
      activeOpacity={0.9}
    >
      {/* Product Image */}
      <View className="relative">
        <Image
          source={{ uri: product.image }}
          className="w-full h-32"
          resizeMode="cover"
        />
        
        {/* Discount Badge */}
        {discount > 0 && (
          <View className="absolute top-2 left-2 bg-accent-500 rounded-full px-2 py-1">
            <Text className="text-xs font-bold text-white">
              {discount}% OFF
            </Text>
          </View>
        )}

        {/* Stock Status */}
        {isOutOfStock && (
          <View className="absolute inset-0 bg-black bg-opacity-50 items-center justify-center">
            <Text className="text-white font-semibold">Out of Stock</Text>
          </View>
        )}

        {/* Low Stock Badge */}
        {!isOutOfStock && isLowStock && (
          <View className="absolute top-2 right-2 bg-red-500 rounded-full px-2 py-1">
            <Text className="text-xs font-bold text-white">
              Only {product.stockQuantity} left
            </Text>
          </View>
        )}

        {/* Freshness Indicator */}
        {product.tags.includes('fresh') && (
          <View className="absolute top-2 right-2 bg-primary-500 rounded-full p-1">
            <Ionicons name="leaf" size={12} color="#ffffff" />
          </View>
        )}
      </View>

      {/* Product Info */}
      <View className="p-3">
        <Text className="text-sm font-semibold text-neutral-800 mb-1" numberOfLines={2}>
          {product.name}
        </Text>
        
        <Text className="text-xs text-neutral-600 mb-2" numberOfLines={1}>
          {product.unit}
        </Text>

        {/* Rating */}
        <View className="flex-row items-center mb-2">
          <Ionicons name="star" size={12} color="#fbbf24" />
          <Text className="text-xs text-neutral-600 ml-1">
            {product.rating} ({product.reviewCount})
          </Text>
        </View>

        {/* Price */}
        <View className="flex-row items-center justify-between">
          <View>
            <Text className="text-base font-bold text-neutral-800">
              {formatPrice(product.price)}
            </Text>
            {product.originalPrice && (
              <Text className="text-xs text-neutral-500 line-through">
                {formatPrice(product.originalPrice)}
              </Text>
            )}
          </View>

          {/* Add to Cart Button */}
          <TouchableOpacity
            className={`w-8 h-8 rounded-full items-center justify-center ${
              !isOutOfStock
                ? 'bg-primary-500'
                : 'bg-neutral-300'
            }`}
            onPress={(e) => {
              e.stopPropagation();
              if (!isOutOfStock) {
                handleAddToCart();
              }
            }}
            disabled={isOutOfStock}
          >
            <Ionicons
              name="add"
              size={16}
              color={!isOutOfStock ? "#ffffff" : "#94a3b8"}
            />
          </TouchableOpacity>
        </View>

        {/* Tags */}
        {product.tags.length > 0 && (
          <View className="flex-row flex-wrap mt-2">
            {product.tags.slice(0, 2).map((tag, index) => (
              <View 
                key={index}
                className={`px-2 py-1 rounded-full mr-1 mb-1 ${
                  tag === 'organic' ? 'bg-primary-100' :
                  tag === 'fresh' ? 'bg-primary-100' :
                  tag === 'discount' ? 'bg-accent-100' :
                  'bg-neutral-100'
                }`}
              >
                <Text 
                  className={`text-xs font-medium ${
                    tag === 'organic' ? 'text-primary-700' :
                    tag === 'fresh' ? 'text-primary-700' :
                    tag === 'discount' ? 'text-accent-700' :
                    'text-neutral-700'
                  }`}
                >
                  {tag}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ProductCard;
