import React, { useState } from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity,
  RefreshControl,
  Image
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { Order } from '../types';
import { formatPrice, formatDate } from '../utils';
import { useAuth } from '../hooks/useAuth';

import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';

const OrderHistoryScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'delivered' | 'cancelled'>('all');
  const [page, setPage] = useState(1);

  // Fetch orders
  const {
    data: ordersData,
    isLoading,
    refetch,
    error
  } = useQuery({
    queryKey: ['orders', user?.id, page],
    queryFn: () => api.getOrders(page, 20), // Paginated orders
    enabled: !!user?.id,
  });

  const orders = ordersData?.data || [];

  const filteredOrders = orders.filter(order => {
    if (selectedFilter === 'all') return true;
    return order.status === selectedFilter;
  });

  const handleOrderPress = (order: Order) => {
    navigation.navigate('OrderDetails' as never, {
      orderId: order.id
    } as never);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'confirmed':
        return 'text-blue-600 bg-blue-100';
      case 'preparing':
        return 'text-orange-600 bg-orange-100';
      case 'out_for_delivery':
        return 'text-purple-600 bg-purple-100';
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-neutral-600 bg-neutral-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const renderFilterButton = (filter: typeof selectedFilter, label: string) => (
    <TouchableOpacity
      className={`px-4 py-2 rounded-full mr-3 ${
        selectedFilter === filter 
          ? 'bg-primary-500' 
          : 'bg-neutral-100'
      }`}
      onPress={() => setSelectedFilter(filter)}
    >
      <Text className={`text-sm font-semibold ${
        selectedFilter === filter 
          ? 'text-white' 
          : 'text-neutral-700'
      }`}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderOrderItem = ({ item: order }: { item: Order }) => (
    <TouchableOpacity
      className="bg-white rounded-2xl p-4 shadow-sm border border-neutral-100 mb-3"
      onPress={() => handleOrderPress(order)}
      activeOpacity={0.7}
    >
      <View className="flex-row justify-between items-start mb-3">
        <View className="flex-1">
          <Text className="text-base font-bold text-neutral-800 mb-1">
            Order #{order.id.slice(-8).toUpperCase()}
          </Text>
          <Text className="text-sm text-neutral-600">
            {formatDate(order.orderDate)}
          </Text>
        </View>
        <View className={`px-3 py-1 rounded-full ${getStatusColor(order.status)}`}>
          <Text className={`text-xs font-semibold ${getStatusColor(order.status).split(' ')[0]}`}>
            {getStatusText(order.status)}
          </Text>
        </View>
      </View>

      {/* Order Items Preview */}
      <View className="flex-row items-center mb-3">
        <View className="flex-row -space-x-2 mr-3">
          {order.items.slice(0, 3).map((item, index) => (
            <Image
              key={index}
              source={{ uri: item.product.image }}
              className="w-8 h-8 rounded-full border-2 border-white"
            />
          ))}
          {order.items.length > 3 && (
            <View className="w-8 h-8 rounded-full bg-neutral-200 border-2 border-white items-center justify-center">
              <Text className="text-xs font-bold text-neutral-600">
                +{order.items.length - 3}
              </Text>
            </View>
          )}
        </View>
        <Text className="text-sm text-neutral-600 flex-1">
          {order.items.length} item{order.items.length !== 1 ? 's' : ''}
        </Text>
        <Text className="text-lg font-bold text-neutral-800">
          {formatPrice(order.total)}
        </Text>
      </View>

      {/* Delivery Info */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center">
          <Ionicons name="location" size={16} color="#64748b" />
          <Text className="text-sm text-neutral-600 ml-1">
            {order.deliveryAddress.city}, {order.deliveryAddress.state}
          </Text>
        </View>
        <View className="flex-row items-center">
          <Ionicons name="time" size={16} color="#64748b" />
          <Text className="text-sm text-neutral-600 ml-1">
            {order.status === 'delivered' 
              ? 'Delivered' 
              : formatDate(order.estimatedDelivery)
            }
          </Text>
        </View>
      </View>

      {/* Track Order Button for Active Orders */}
      {order.status !== 'delivered' && order.status !== 'cancelled' && (
        <View className="mt-3 pt-3 border-t border-neutral-100">
          <TouchableOpacity
            className="bg-primary-50 border border-primary-200 rounded-lg px-4 py-2 flex-row items-center justify-center"
            onPress={(e) => {
              e.stopPropagation();
              navigation.navigate('OrderTracking' as never, {
                orderId: order.id
              } as never);
            }}
            activeOpacity={0.7}
          >
            <Ionicons name="location" size={16} color="#22c55e" />
            <Text className="text-sm font-semibold text-primary-600 ml-2">
              Track Order
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View className="flex-1 items-center justify-center px-6 py-12">
      <Ionicons name="receipt-outline" size={80} color="#cbd5e1" />
      <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
        No Orders Yet
      </Text>
      <Text className="text-base text-neutral-600 text-center mb-8">
        {selectedFilter === 'all' 
          ? "You haven't placed any orders yet. Start shopping to see your orders here!"
          : `No ${selectedFilter} orders found.`
        }
      </Text>
      {selectedFilter === 'all' && (
        <Button
          title="Start Shopping"
          onPress={() => navigation.navigate('Main' as never)}
          size="lg"
        />
      )}
    </View>
  );

  if (isLoading) {
    return <LoadingSpinner message="Loading orders..." fullScreen />;
  }

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 shadow-sm">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <TouchableOpacity
              className="mr-4"
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={24} color="#1e293b" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-neutral-800">
              Order History
            </Text>
          </View>
          <TouchableOpacity onPress={refetch}>
            <Ionicons name="refresh" size={24} color="#64748b" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Tabs */}
      <View className="bg-white px-4 py-3 border-b border-neutral-100">
        <FlatList
          data={[
            { key: 'all', label: 'All' },
            { key: 'pending', label: 'Pending' },
            { key: 'delivered', label: 'Delivered' },
            { key: 'cancelled', label: 'Cancelled' },
          ]}
          renderItem={({ item }) => renderFilterButton(item.key as any, item.label)}
          keyExtractor={(item) => item.key}
          horizontal
          showsHorizontalScrollIndicator={false}
        />
      </View>

      {/* Orders List */}
      <View className="flex-1 px-4 pt-4">
        {error ? (
          <View className="flex-1 items-center justify-center px-6">
            <Ionicons name="alert-circle" size={80} color="#ef4444" />
            <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
              Failed to Load Orders
            </Text>
            <Text className="text-base text-neutral-600 text-center mb-8">
              Something went wrong while loading your orders.
            </Text>
            <Button
              title="Try Again"
              onPress={() => refetch()}
              variant="primary"
              size="md"
            />
          </View>
        ) : (
          <FlatList
            data={filteredOrders}
            renderItem={renderOrderItem}
            keyExtractor={(item) => item.id}
            ListEmptyComponent={renderEmptyState}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={refetch} />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 80 }}
          />
        )}

        {/* Pagination Controls */}
        {ordersData?.pagination && ordersData.pagination.totalPages > 1 && (
          <View className="bg-white border-t border-neutral-200 px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Button
                title="Previous"
                onPress={() => setPage(Math.max(1, page - 1))}
                variant="outline"
                size="sm"
                disabled={page <= 1 || isLoading}
              />
              <Text className="text-sm text-neutral-600">
                Page {page} of {ordersData.pagination.totalPages}
              </Text>
              <Button
                title="Next"
                onPress={() => setPage(Math.min(ordersData.pagination.totalPages, page + 1))}
                variant="outline"
                size="sm"
                disabled={page >= ordersData.pagination.totalPages || isLoading}
              />
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

export default OrderHistoryScreen;
