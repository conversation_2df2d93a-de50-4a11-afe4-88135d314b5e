package com.grocease.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grocease.dto.banner.BannerDto;
import com.grocease.dto.discount.DiscountCodeDto;
import com.grocease.dto.order.OrderDto;
import com.grocease.dto.order.OrderItemDto;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.ProductDto;
import com.grocease.dto.user.AddressDto;
import com.grocease.dto.user.UserDto;
import com.grocease.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class DtoMapper {

    private final ObjectMapper objectMapper;

    public UserDto toUserDto(User user) {
        return UserDto.builder()
                .id(String.valueOf(user.getId()))
                .name(user.getName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatar(user.getAvatar())
                .role(user.getRole().name())
                .isEmailVerified(user.getIsEmailVerified())
                .isActive(user.getIsActive())
                .createdAt(user.getCreatedAt())
                .addresses(user.getAddresses() != null ?
                        user.getAddresses().stream()
                                .map(this::toAddressDto)
                                .collect(Collectors.toList()) :
                        new ArrayList<>())
                .build();
    }

    public UserDto toUserDtoWithoutAddresses(User user) {
        return UserDto.builder()
                .id(String.valueOf(user.getId()))
                .name(user.getName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .avatar(user.getAvatar())
                .role(user.getRole().name())
                .isEmailVerified(user.getIsEmailVerified())
                .isActive(user.getIsActive())
                .createdAt(user.getCreatedAt())
                .addresses(new ArrayList<>()) // Empty list to avoid lazy loading
                .build();
    }

    public AddressDto toAddressDto(Address address) {
        Address.AddressType addressType = null;

        try {
            // Safely access address type
            addressType = address.getType();
        } catch (org.hibernate.LazyInitializationException e) {
            // If type is not loaded, use null
            log.warn("LazyInitializationException for address {} type, using null. This should not happen with proper JOIN FETCH.", address.getId());
            addressType = null;
        } catch (Exception e) {
            // Any other exception
            log.error("Unexpected error accessing type for address {}: {}", address.getId(), e.getMessage());
            addressType = null;
        }

        return AddressDto.builder()
                .id(String.valueOf(address.getId()))
                .type(addressType)
                .street(address.getStreet())
                .city(address.getCity())
                .state(address.getState())
                .zipCode(address.getZipCode())
                .isDefault(address.getIsDefault())
                .build();
    }

    public ProductDto toProductDto(Product product) {
        List<String> tags = new ArrayList<>();
        String categoryName = null;

        try {
            // Safely access tags - if they're not loaded, this will throw LazyInitializationException
            if (product.getTags() != null && !product.getTags().isEmpty()) {
                tags = product.getTags().stream()
                        .map(ProductTag::getName)
                        .collect(Collectors.toList());
            }
        } catch (org.hibernate.LazyInitializationException e) {
            // If tags are not loaded, just use empty list
            log.warn("LazyInitializationException for product {} tags, using empty list. This should not happen with proper JOIN FETCH.", product.getId());
            tags = new ArrayList<>();
        } catch (Exception e) {
            // Any other exception
            log.error("Unexpected error accessing tags for product {}: {}", product.getId(), e.getMessage());
            tags = new ArrayList<>();
        }

        try {
            // Safely access category
            if (product.getCategory() != null) {
                categoryName = product.getCategory().getName();
            }
        } catch (org.hibernate.LazyInitializationException e) {
            // If category is not loaded, use null
            log.warn("LazyInitializationException for product {} category, using null. This should not happen with proper JOIN FETCH.", product.getId());
            categoryName = null;
        } catch (Exception e) {
            // Any other exception
            log.error("Unexpected error accessing category for product {}: {}", product.getId(), e.getMessage());
            categoryName = null;
        }

        return ProductDto.builder()
                .id(String.valueOf(product.getId()))
                .name(product.getName())
                .description(product.getDescription())
                .price(product.getPrice())
                .originalPrice(product.getOriginalPrice())
                .discount(product.getDiscount())
                .image(product.getImage())
                .category(categoryName)
                .unit(product.getUnit())
                .inStock(product.getInStock())
                .rating(product.getRating())
                .reviewCount(product.getReviewCount())
                .tags(tags)
                .stockQuantity(product.getStockQuantity())
                .build();
    }

    public CategoryDto toCategoryDto(Category category) {
        return CategoryDto.builder()
                .id(String.valueOf(category.getId()))
                .name(category.getName())
                .image(category.getImage())
                .icon(category.getIcon())
                .color(category.getColor())
                .isActive(category.getIsActive())
                .sortOrder(category.getSortOrder())
                .build();
    }

    public BannerDto toBannerDto(Banner banner) {
        return BannerDto.builder()
                .id(String.valueOf(banner.getId()))
                .title(banner.getTitle())
                .subtitle(banner.getSubtitle())
                .image(banner.getImage())
                .backgroundColor(banner.getBackgroundColor())
                .textColor(banner.getTextColor())
                .actionText(banner.getActionText())
                .actionUrl(banner.getActionUrl())
                .isActive(banner.getIsActive())
                .sortOrder(banner.getSortOrder())
                .createdAt(banner.getCreatedAt().toString())
                .build();
    }

    public OrderDto toOrderDto(Order order) {
        if (order == null) {
            log.error("Order is null in toOrderDto");
            throw new IllegalArgumentException("Order cannot be null");
        }

        if (order.getId() == null) {
            log.error("Order ID is null in toOrderDto for order: {}", order.getOrderNumber());
            throw new IllegalArgumentException("Order ID cannot be null");
        }

        // Get customer information safely
        String customerName = "Unknown";
        String customerEmail = "No email";

        try {
            if (order.getUser() != null) {
                customerName = order.getUser().getName() != null ? order.getUser().getName() : "Unknown";
                customerEmail = order.getUser().getEmail() != null ? order.getUser().getEmail() : "No email";
            }
        } catch (Exception e) {
            log.warn("Error accessing user information for order {}: {}", order.getId(), e.getMessage());
        }

        return OrderDto.builder()
                .id(String.valueOf(order.getId()))
                .orderNumber(order.getOrderNumber())
                .total(order.getTotal())
                .subtotal(order.getSubtotal())
                .deliveryFee(order.getDeliveryFee())
                .discount(order.getDiscount())
                .discountCode(order.getDiscountCode())
                .status(order.getStatus())
                .deliveryAddress(order.getDeliveryAddress() != null ? toAddressDto(order.getDeliveryAddress()) : null)
                .orderDate(order.getCreatedAt())
                .estimatedDelivery(order.getEstimatedDelivery())
                .customerName(customerName)
                .customerEmail(customerEmail)
                .items(order.getItems() != null ?
                        order.getItems().stream()
                                .map(this::toOrderItemDto)
                                .collect(Collectors.toList()) :
                        new ArrayList<>())
                .build();
    }

    public OrderItemDto toOrderItemDto(OrderItem orderItem) {
        return OrderItemDto.builder()
                .product(toProductDto(orderItem.getProduct()))
                .quantity(orderItem.getQuantity())
                .unitPrice(orderItem.getUnitPrice())
                .totalPrice(orderItem.getTotalPrice())
                .build();
    }

    public List<ProductDto> toProductDtoList(List<Product> products) {
        return products.stream()
                .map(this::toProductDto)
                .collect(Collectors.toList());
    }

    public List<CategoryDto> toCategoryDtoList(List<Category> categories) {
        return categories.stream()
                .map(this::toCategoryDto)
                .collect(Collectors.toList());
    }

    public List<BannerDto> toBannerDtoList(List<Banner> banners) {
        return banners.stream()
                .map(this::toBannerDto)
                .collect(Collectors.toList());
    }

    public List<OrderDto> toOrderDtoList(List<Order> orders) {
        return orders.stream()
                .map(this::toOrderDto)
                .collect(Collectors.toList());
    }

    public DiscountCodeDto toDiscountCodeDto(DiscountCode discountCode) {
        List<String> applicableCategories = new ArrayList<>();
        List<String> applicableProducts = new ArrayList<>();

        try {
            if (discountCode.getApplicableCategories() != null) {
                List<Long> categoryIds = objectMapper.readValue(
                        discountCode.getApplicableCategories(), new TypeReference<List<Long>>() {});
                applicableCategories = categoryIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
            }

            if (discountCode.getApplicableProducts() != null) {
                List<Long> productIds = objectMapper.readValue(
                        discountCode.getApplicableProducts(), new TypeReference<List<Long>>() {});
                applicableProducts = productIds.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
            }
        } catch (JsonProcessingException e) {
            log.error("Error parsing applicable categories/products for discount code: {}", discountCode.getCode(), e);
        }

        return DiscountCodeDto.builder()
                .id(String.valueOf(discountCode.getId()))
                .code(discountCode.getCode())
                .name(discountCode.getName())
                .description(discountCode.getDescription())
                .type(discountCode.getType())
                .value(discountCode.getValue())
                .minimumOrderAmount(discountCode.getMinimumOrderAmount())
                .maximumDiscountAmount(discountCode.getMaximumDiscountAmount())
                .usageLimit(discountCode.getUsageLimit())
                .usageLimitPerUser(discountCode.getUsageLimitPerUser())
                .usedCount(discountCode.getUsedCount())
                .validFrom(discountCode.getValidFrom())
                .validUntil(discountCode.getValidUntil())
                .isActive(discountCode.getIsActive())
                .isFirstOrderOnly(discountCode.getIsFirstOrderOnly())
                .applicableCategories(applicableCategories)
                .applicableProducts(applicableProducts)
                .createdAt(discountCode.getCreatedAt())
                .isValid(discountCode.isValid())
                .isExpired(discountCode.isExpired())
                .build();
    }

    public List<DiscountCodeDto> toDiscountCodeDtoList(List<DiscountCode> discountCodes) {
        return discountCodes.stream()
                .map(this::toDiscountCodeDto)
                .collect(Collectors.toList());
    }
}
