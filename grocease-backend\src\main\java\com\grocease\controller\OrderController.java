package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.order.CreateOrderRequest;
import com.grocease.dto.order.OrderDto;
import com.grocease.entity.User;
import com.grocease.service.OrderService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orders")
@RequiredArgsConstructor
@Slf4j
public class OrderController {

    private final OrderService orderService;

    @PostMapping
    public ResponseEntity<ApiResponse<OrderDto>> createOrder(
            @AuthenticationPrincipal User user,
            @Valid @RequestBody CreateOrderRequest request) {
        log.info("Creating order for user: {}", user.getId());
        OrderDto order = orderService.createOrder(user.getId(), request);
        return ResponseEntity.ok(ApiResponse.success(order, "Order created successfully"));
    }

    @GetMapping
    public ResponseEntity<PaginatedResponse<OrderDto>> getUserOrders(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit) {

        // Validate pagination parameters at controller level (1-based)
        page = Math.max(1, page);
        limit = Math.max(1, Math.min(100, limit));

        log.info("Getting orders for user: {} - page: {}, limit: {}", user.getId(), page, limit);
        PaginatedResponse<OrderDto> response = orderService.getUserOrders(user.getId(), page, limit);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{orderId}")
    public ResponseEntity<ApiResponse<OrderDto>> getOrderById(
            @AuthenticationPrincipal User user,
            @PathVariable Long orderId) {
        log.info("Getting order {} for user: {}", orderId, user.getId());
        OrderDto order = orderService.getOrderById(user.getId(), orderId);
        return ResponseEntity.ok(ApiResponse.success(order, "Order retrieved successfully"));
    }

    @GetMapping("/number/{orderNumber}")
    public ResponseEntity<ApiResponse<OrderDto>> getOrderByNumber(
            @AuthenticationPrincipal User user,
            @PathVariable String orderNumber) {
        log.info("Getting order {} for user: {}", orderNumber, user.getId());
        OrderDto order = orderService.getOrderByNumber(user.getId(), orderNumber);
        return ResponseEntity.ok(ApiResponse.success(order, "Order retrieved successfully"));
    }

    @GetMapping("/test")
    public ResponseEntity<ApiResponse<String>> testUserOrders(@AuthenticationPrincipal User user) {
        log.info("Test endpoint called by user: {} ({})", user.getEmail(), user.getId());
        return ResponseEntity.ok(ApiResponse.success(
            "User orders endpoint is working for user: " + user.getEmail(),
            "Test successful"
        ));
    }
}
