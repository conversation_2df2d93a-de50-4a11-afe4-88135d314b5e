package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.analytics.DashboardOverviewDto;
import com.grocease.dto.notification.NotificationDto;
import com.grocease.dto.notification.SendNotificationRequest;
import com.grocease.dto.order.UpdateOrderStatusRequest;
import com.grocease.dto.order.OrderDto;
import com.grocease.service.AnalyticsService;
import com.grocease.service.NotificationService;
import com.grocease.service.OrderService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final AnalyticsService analyticsService;
    private final NotificationService notificationService;
    private final OrderService orderService;

    @GetMapping("/dashboard/overview")
    public ResponseEntity<ApiResponse<DashboardOverviewDto>> getDashboardOverview() {
        log.info("Getting admin dashboard overview");
        DashboardOverviewDto overview = analyticsService.getDashboardOverview();
        return ResponseEntity.ok(ApiResponse.success(overview, "Dashboard overview retrieved successfully"));
    }

    @GetMapping("/test")
    public ResponseEntity<ApiResponse<String>> testEndpoint() {
        log.info("Admin test endpoint called");
        return ResponseEntity.ok(ApiResponse.success("Admin endpoint is working!", "Test successful"));
    }

    // Admin Order Management Endpoints
    @GetMapping("/orders")
    public ResponseEntity<PaginatedResponse<OrderDto>> getAllOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        // Validate pagination parameters at controller level (1-based)
        page = Math.max(1, page);
        limit = Math.max(1, Math.min(100, limit));

        log.info("Admin getting all orders - page: {}, limit: {}, search: {}, status: {}",
                page, limit, search, status);

        PaginatedResponse<OrderDto> response = orderService.getAllOrdersForAdmin(page, limit, search, status, sortBy, sortDir);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/orders/{orderId}")
    public ResponseEntity<ApiResponse<OrderDto>> getOrderById(@PathVariable Long orderId) {
        log.info("Admin getting order by id: {}", orderId);
        OrderDto order = orderService.getOrderByIdForAdmin(orderId);
        return ResponseEntity.ok(ApiResponse.success(order, "Order retrieved successfully"));
    }

    @PutMapping("/orders/{orderId}/status")
    public ResponseEntity<ApiResponse<OrderDto>> updateOrderStatus(
            @PathVariable Long orderId,
            @Valid @RequestBody UpdateOrderStatusRequest request) {
        log.info("Admin updating order {} status to {}", orderId, request.getStatus());
        OrderDto updatedOrder = orderService.updateOrderStatus(orderId, request.getStatus());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "Order status updated successfully"));
    }

    @PostMapping("/notifications/send")
    public ResponseEntity<ApiResponse<String>> sendNotification(
            @Valid @RequestBody SendNotificationRequest request) {
        log.info("Admin sending notification: {}", request.getTitle());
        
        // Check rate limiting for broadcasts
        if (request.getIsBroadcast() && !notificationService.canSendBroadcast()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Rate limit exceeded", "Too many broadcasts sent recently"));
        }
        
        notificationService.sendNotification(request);
        return ResponseEntity.ok(ApiResponse.success("Notification sent successfully", 
                "Notification has been queued for delivery"));
    }

    @GetMapping("/notifications/history")
    public ResponseEntity<Page<NotificationDto>> getNotificationHistory(
            @RequestParam(required = false) Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        log.info("Getting notification history - userId: {}, page: {}, size: {}", userId, page, size);
        
        Pageable pageable = PageRequest.of(page, size);
        Page<NotificationDto> notifications = notificationService.getNotificationHistory(userId, pageable);
        return ResponseEntity.ok(notifications);
    }
}
