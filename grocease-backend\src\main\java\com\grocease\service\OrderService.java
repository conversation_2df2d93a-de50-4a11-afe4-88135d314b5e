package com.grocease.service;

import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.order.CreateOrderRequest;
import com.grocease.dto.order.OrderDto;
import com.grocease.entity.*;
import com.grocease.exception.BadRequestException;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.*;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderService {

    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final ProductRepository productRepository;
    private final AddressRepository addressRepository;
    private final OrderStatusHistoryRepository orderStatusHistoryRepository;
    private final DiscountCodeService discountCodeService;
    private final DtoMapper dtoMapper;
    private final NotificationService notificationService;

    @Transactional
    public OrderDto createOrder(Long userId, CreateOrderRequest request) {
        try {
            log.info("Creating order for user: {} with request: {}", userId, request);

            // Validate user
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

            // Validate delivery address
            Address deliveryAddress = addressRepository.findById(request.getDeliveryAddressId())
                    .orElseThrow(() -> new ResourceNotFoundException("Address not found with id: " + request.getDeliveryAddressId()));

            if (!deliveryAddress.getUser().getId().equals(userId)) {
                throw new BadRequestException("Address does not belong to user");
            }

        // Create order
        Order order = Order.builder()
                .orderNumber(generateOrderNumber())
                .total(request.getTotal())
                .subtotal(request.getSubtotal())
                .deliveryFee(request.getDeliveryFee())
                .discount(request.getDiscount())
                .discountCode(request.getDiscountCode())
                .status(Order.OrderStatus.PENDING)
                .estimatedDelivery(LocalDateTime.now().plusHours(2)) // 2 hours from now
                .user(user)
                .deliveryAddress(deliveryAddress)
                .items(new ArrayList<>())
                .build();

        // Create order items
        BigDecimal calculatedSubtotal = BigDecimal.ZERO;
        for (CreateOrderRequest.OrderItemRequest itemRequest : request.getItems()) {
            Product product = productRepository.findById(itemRequest.getProductId())
                    .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + itemRequest.getProductId()));

            if (!product.getInStock()) {
                throw new BadRequestException("Product is out of stock: " + product.getName());
            }

            // Handle null stockQuantity - treat as unlimited stock if null
            Integer currentStock = product.getStockQuantity();
            if (currentStock != null && currentStock < itemRequest.getQuantity()) {
                throw new BadRequestException("Insufficient stock for product: " + product.getName() +
                    ". Available: " + currentStock + ", Requested: " + itemRequest.getQuantity());
            }

            BigDecimal itemTotal = product.getPrice().multiply(BigDecimal.valueOf(itemRequest.getQuantity()));
            calculatedSubtotal = calculatedSubtotal.add(itemTotal);

            OrderItem orderItem = OrderItem.builder()
                    .quantity(itemRequest.getQuantity())
                    .unitPrice(product.getPrice())
                    .totalPrice(itemTotal)
                    .order(order)
                    .product(product)
                    .build();

            order.getItems().add(orderItem);

            // Update product stock - only if stockQuantity is not null (finite stock)
            if (currentStock != null) {
                product.setStockQuantity(currentStock - itemRequest.getQuantity());
                productRepository.save(product);
            }
        }

        // Validate calculated totals
        if (calculatedSubtotal.compareTo(request.getSubtotal()) != 0) {
            throw new BadRequestException("Subtotal mismatch. Expected: " + calculatedSubtotal + ", Received: " + request.getSubtotal());
        }

        BigDecimal calculatedTotal = calculatedSubtotal.add(request.getDeliveryFee()).subtract(request.getDiscount());
        if (calculatedTotal.compareTo(request.getTotal()) != 0) {
            throw new BadRequestException("Total mismatch. Expected: " + calculatedTotal + ", Received: " + request.getTotal());
        }

        // Save order
        log.info("Saving order to database...");
        Order savedOrder = orderRepository.save(order);
        log.info("Order saved with ID: {}", savedOrder.getId());

        // Record discount code usage if applicable
        if (request.getDiscountCode() != null && !request.getDiscountCode().trim().isEmpty()) {
            try {
                log.info("Recording discount code usage for order: {}", savedOrder.getId());
                discountCodeService.recordDiscountUsage(
                    request.getDiscountCode(),
                    userId,
                    savedOrder.getId(),
                    request.getDiscount(),
                    request.getSubtotal()
                );
                log.info("Discount code usage recorded: {} for order: {}", request.getDiscountCode(), savedOrder.getOrderNumber());
            } catch (Exception e) {
                log.error("Failed to record discount code usage: {} for order: {}", request.getDiscountCode(), savedOrder.getOrderNumber(), e);
                // Don't fail the order creation if discount recording fails
            }
        }

        // Update order status to confirmed
        log.info("Updating order status to CONFIRMED for order: {}", savedOrder.getId());
        savedOrder.setStatus(Order.OrderStatus.CONFIRMED);
        savedOrder = orderRepository.save(savedOrder);
        log.info("Order status updated successfully for order: {}", savedOrder.getId());

            log.info("Order created successfully: {} for user: {}", savedOrder.getOrderNumber(), userId);

            log.info("Converting order to DTO for order: {}", savedOrder.getId());
            OrderDto orderDto = dtoMapper.toOrderDto(savedOrder);
            log.info("Order DTO created successfully with ID: {}", orderDto.getId());

            return orderDto;

        } catch (Exception e) {
            log.error("Error creating order for user: {} with request: {}", userId, request, e);
            throw e; // Re-throw the exception to maintain transaction rollback
        }
    }

    public PaginatedResponse<OrderDto> getUserOrders(Long userId, int page, int limit) {
        // Validate and sanitize pagination parameters (1-based input)
        page = Math.max(1, page); // Ensure page is at least 1
        limit = Math.max(1, Math.min(100, limit)); // Ensure limit is between 1 and 100

        log.info("Getting orders for user {} with validated params - page: {}, limit: {}", userId, page, limit);

        try {
            // Convert 1-based page to 0-based for Spring Data
            Pageable pageable = PageRequest.of(page - 1, limit);
            Page<Order> orderPage = orderRepository.findByUserIdWithDetails(userId, pageable);

            // Load product tags separately to avoid MultipleBagFetchException
            for (Order order : orderPage.getContent()) {
                loadProductTagsForOrder(order);
            }

            List<OrderDto> orderDtos = dtoMapper.toOrderDtoList(orderPage.getContent());

            return PaginatedResponse.<OrderDto>builder()
                    .data(orderDtos)
                    .pagination(PaginatedResponse.PaginationInfo.builder()
                            .page(page) // Return 1-based page to client
                            .limit(limit)
                            .total(orderPage.getTotalElements())
                            .totalPages(orderPage.getTotalPages())
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("Error fetching orders for user {}: {}", userId, e.getMessage(), e);
            // Return empty result on error
            return PaginatedResponse.<OrderDto>builder()
                    .data(new ArrayList<>())
                    .pagination(PaginatedResponse.PaginationInfo.builder()
                            .page(page) // Return 1-based page to client
                            .limit(limit)
                            .total(0L)
                            .totalPages(0)
                            .build())
                    .build();
        }
    }

    public OrderDto getOrderById(Long userId, Long orderId) {
        Order order = orderRepository.findByIdWithDetails(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with id: " + orderId));

        if (!order.getUser().getId().equals(userId)) {
            throw new BadRequestException("Order does not belong to user");
        }

        return dtoMapper.toOrderDto(order);
    }

    public OrderDto getOrderByNumber(Long userId, String orderNumber) {
        Order order = orderRepository.findByOrderNumberWithDetails(orderNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with number: " + orderNumber));

        if (!order.getUser().getId().equals(userId)) {
            throw new BadRequestException("Order does not belong to user");
        }

        return dtoMapper.toOrderDto(order);
    }

    @Transactional
    public OrderDto updateOrderStatus(Long orderId, Order.OrderStatus newStatus) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with id: " + orderId));

        Order.OrderStatus currentStatus = order.getStatus();

        // Validate status transition
        validateStatusTransition(currentStatus, newStatus);

        // Record status change in history
        OrderStatusHistory statusHistory = OrderStatusHistory.builder()
                .order(order)
                .fromStatus(currentStatus)
                .toStatus(newStatus)
                .changedBy("ADMIN") // In a real app, this would be the current user
                .notes("Status updated by admin")
                .build();

        orderStatusHistoryRepository.save(statusHistory);

        // Update order status
        order.setStatus(newStatus);

        if (newStatus == Order.OrderStatus.DELIVERED) {
            order.setDeliveredAt(LocalDateTime.now());
        }

        Order savedOrder = orderRepository.save(order);

        // Send notification to user about status change
        sendOrderStatusNotification(savedOrder, newStatus);

        log.info("Order {} status updated from {} to {}", orderId, currentStatus, newStatus);

        return dtoMapper.toOrderDto(savedOrder);
    }

    // Admin-specific methods
    public PaginatedResponse<OrderDto> getAllOrdersForAdmin(int page, int limit, String search, String status, String sortBy, String sortDir) {
        log.info("Getting all orders for admin - page: {}, limit: {}, search: {}, status: {}", page, limit, search, status);

        try {
            // Convert 1-based page to 0-based for Spring Data
            Pageable pageable = PageRequest.of(page - 1, limit,
                Sort.by(Sort.Direction.fromString(sortDir), sortBy));

            Page<Order> orderPage;

            // For now, let's just get all orders and filter later
            // TODO: Implement proper search functionality
            if (status != null && !status.trim().isEmpty() && !status.equalsIgnoreCase("ALL")) {
                Order.OrderStatus orderStatus = Order.OrderStatus.valueOf(status.toUpperCase());
                orderPage = orderRepository.findByStatusWithDetails(orderStatus, pageable);
            } else {
                orderPage = orderRepository.findAllWithDetails(pageable);
            }

            // Load product tags separately to avoid MultipleBagFetchException
            for (Order order : orderPage.getContent()) {
                loadProductTagsForOrder(order);
            }

            List<OrderDto> orderDtos = dtoMapper.toOrderDtoList(orderPage.getContent());

            return PaginatedResponse.<OrderDto>builder()
                    .data(orderDtos)
                    .pagination(PaginatedResponse.PaginationInfo.builder()
                            .page(page) // Return 1-based page to client
                            .limit(limit)
                            .total(orderPage.getTotalElements())
                            .totalPages(orderPage.getTotalPages())
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("Error fetching all orders for admin: {}", e.getMessage(), e);
            // Return empty result on error
            return PaginatedResponse.<OrderDto>builder()
                    .data(new ArrayList<>())
                    .pagination(PaginatedResponse.PaginationInfo.builder()
                            .page(page)
                            .limit(limit)
                            .total(0L)
                            .totalPages(0)
                            .build())
                    .build();
        }
    }

    public OrderDto getOrderByIdForAdmin(Long orderId) {
        log.info("Getting order {} for admin", orderId);
        Order order = orderRepository.findByIdWithDetails(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found with id: " + orderId));

        // Load product tags separately to avoid MultipleBagFetchException
        loadProductTagsForOrder(order);

        return dtoMapper.toOrderDto(order);
    }

    private void loadProductTagsForOrder(Order order) {
        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                if (item.getProduct() != null) {
                    // Force loading of tags by accessing them
                    try {
                        item.getProduct().getTags().size();
                    } catch (Exception e) {
                        log.warn("Could not load tags for product {}: {}", item.getProduct().getId(), e.getMessage());
                    }
                }
            }
        }
    }

    private void validateStatusTransition(Order.OrderStatus currentStatus, Order.OrderStatus newStatus) {
        Map<Order.OrderStatus, Set<Order.OrderStatus>> validTransitions = Map.of(
            Order.OrderStatus.PENDING, Set.of(Order.OrderStatus.CONFIRMED, Order.OrderStatus.CANCELLED),
            Order.OrderStatus.CONFIRMED, Set.of(Order.OrderStatus.PREPARING, Order.OrderStatus.CANCELLED),
            Order.OrderStatus.PREPARING, Set.of(Order.OrderStatus.OUT_FOR_DELIVERY, Order.OrderStatus.CANCELLED),
            Order.OrderStatus.OUT_FOR_DELIVERY, Set.of(Order.OrderStatus.DELIVERED, Order.OrderStatus.CANCELLED),
            Order.OrderStatus.DELIVERED, Set.of(), // No transitions from delivered
            Order.OrderStatus.CANCELLED, Set.of()  // No transitions from cancelled
        );

        Set<Order.OrderStatus> allowedTransitions = validTransitions.get(currentStatus);
        if (allowedTransitions == null || !allowedTransitions.contains(newStatus)) {
            throw new BadRequestException(
                String.format("Invalid status transition from %s to %s", currentStatus, newStatus));
        }
    }

    private void sendOrderStatusNotification(Order order, Order.OrderStatus newStatus) {
        try {
            String title = getNotificationTitle(newStatus);
            String message = getNotificationMessage(order, newStatus);
            NotificationHistory.NotificationType notificationType = getNotificationType(newStatus);

            if (title != null && message != null && notificationType != null) {
                // This would be called asynchronously in a real application
                // For now, we'll just log it
                log.info("Sending notification to user {}: {} - {}",
                        order.getUser().getId(), title, message);
            }
        } catch (Exception e) {
            log.error("Failed to send order status notification for order: {}", order.getId(), e);
        }
    }

    private String getNotificationTitle(Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> "Order Confirmed";
            case PREPARING -> "Order Being Prepared";
            case OUT_FOR_DELIVERY -> "Order Out for Delivery";
            case DELIVERED -> "Order Delivered";
            case CANCELLED -> "Order Cancelled";
            default -> null;
        };
    }

    private String getNotificationMessage(Order order, Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> String.format("Your order #%s has been confirmed and will be prepared soon.",
                    order.getOrderNumber());
            case PREPARING -> String.format("Your order #%s is being prepared by our team.",
                    order.getOrderNumber());
            case OUT_FOR_DELIVERY -> String.format("Your order #%s is on its way to you!",
                    order.getOrderNumber());
            case DELIVERED -> String.format("Your order #%s has been delivered. Enjoy your groceries!",
                    order.getOrderNumber());
            case CANCELLED -> String.format("Your order #%s has been cancelled. If you have any questions, please contact support.",
                    order.getOrderNumber());
            default -> null;
        };
    }

    private NotificationHistory.NotificationType getNotificationType(Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> NotificationHistory.NotificationType.ORDER_CONFIRMATION;
            case PREPARING -> NotificationHistory.NotificationType.ORDER_PREPARING;
            case OUT_FOR_DELIVERY -> NotificationHistory.NotificationType.ORDER_OUT_FOR_DELIVERY;
            case DELIVERED -> NotificationHistory.NotificationType.ORDER_DELIVERED;
            case CANCELLED -> NotificationHistory.NotificationType.ORDER_CANCELLED;
            default -> null;
        };
    }

    private String generateOrderNumber() {
        String uuid = UUID.randomUUID().toString();
        // Safely extract first 8 characters, ensuring we don't go out of bounds
        String shortUuid = uuid.length() >= 8 ? uuid.substring(0, 8) : uuid;
        return "ORD-" + System.currentTimeMillis() + "-" + shortUuid.toUpperCase();
    }
}
