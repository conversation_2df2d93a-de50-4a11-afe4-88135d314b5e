import React from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity,
  Image,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { formatPrice, formatDate } from '../utils';

import LoadingSpinner from '../components/LoadingSpinner';
import Button from '../components/Button';

type OrderDetailsRouteProp = RouteProp<{
  OrderDetails: { orderId: string };
}, 'OrderDetails'>;

const OrderDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<OrderDetailsRouteProp>();
  const { orderId } = route.params;

  // Fetch order details
  const { 
    data: orderData, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => api.getOrderDetails(orderId),
  });

  const order = orderData?.data;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'CONFIRMED':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'PREPARING':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'OUT_FOR_DELIVERY':
        return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'DELIVERED':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'CANCELLED':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-neutral-600 bg-neutral-100 border-neutral-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Order Pending';
      case 'CONFIRMED':
        return 'Order Confirmed';
      case 'PREPARING':
        return 'Preparing Order';
      case 'OUT_FOR_DELIVERY':
        return 'Out for Delivery';
      case 'DELIVERED':
        return 'Delivered';
      case 'CANCELLED':
        return 'Order Cancelled';
      default:
        return status;
    }
  };

  const getStatusSteps = () => {
    const steps = [
      { key: 'CONFIRMED', label: 'Confirmed', icon: 'checkmark-circle' },
      { key: 'PREPARING', label: 'Preparing', icon: 'restaurant' },
      { key: 'OUT_FOR_DELIVERY', label: 'Out for Delivery', icon: 'car' },
      { key: 'DELIVERED', label: 'Delivered', icon: 'home' },
    ];

    if (!order) return steps;

    const currentStepIndex = steps.findIndex(step => step.key === order.status);
    
    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentStepIndex,
      current: index === currentStepIndex,
    }));
  };

  const handleReorder = () => {
    if (!order) return;
    
    Alert.alert(
      'Reorder Items',
      'Add all items from this order to your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Add to Cart', 
          onPress: () => {
            // Add items to cart logic here
            Alert.alert('Success', 'Items added to cart!');
            navigation.navigate('Cart' as never);
          }
        }
      ]
    );
  };

  const handleTrackOrder = () => {
    if (!order) return;
    navigation.navigate('OrderTracking' as never, {
      orderId: order.id
    } as never);
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Customer Support',
      'Need help with your order?\n\nPhone: +****************\nEmail: <EMAIL>\n\nOur support team is available 24/7.',
      [{ text: 'OK' }]
    );
  };

  if (isLoading) {
    return <LoadingSpinner message="Loading order details..." fullScreen />;
  }

  if (error || !order) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="alert-circle" size={80} color="#ef4444" />
          <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
            Order Not Found
          </Text>
          <Text className="text-base text-neutral-600 text-center mb-8">
            We couldn't find the order you're looking for.
          </Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </SafeAreaView>
    );
  }

  const statusSteps = getStatusSteps();

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      {/* Header */}
      <View className="bg-white px-4 py-4 shadow-sm">
        <View className="flex-row items-center">
          <TouchableOpacity
            className="mr-4"
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#1e293b" />
          </TouchableOpacity>
          <View className="flex-1">
            <Text className="text-xl font-bold text-neutral-800">
              Order Details
            </Text>
            <Text className="text-sm text-neutral-600">
              #{order.id.slice(-8).toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-4 py-6">
          {/* Order Status */}
          <View className={`rounded-2xl p-6 border-2 mb-6 ${getStatusColor(order.status)}`}>
            <View className="items-center">
              <Ionicons
                name={order.status === 'DELIVERED' ? 'checkmark-circle' : 'time'}
                size={48}
                color={getStatusColor(order.status).split(' ')[0].replace('text-', '#')}
              />
              <Text className={`text-xl font-bold mt-3 ${getStatusColor(order.status).split(' ')[0]}`}>
                {getStatusText(order.status)}
              </Text>
              <Text className="text-sm text-neutral-600 mt-1 text-center">
                {order.status === 'DELIVERED'
                  ? `Delivered on ${formatDate(order.orderDate)}`
                  : `Estimated delivery: ${formatDate(order.estimatedDelivery)}`
                }
              </Text>
            </View>
          </View>

          {/* Order Progress */}
          {order.status !== 'CANCELLED' && (
            <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
              <Text className="text-lg font-bold text-neutral-800 mb-4">
                Order Progress
              </Text>
              <View className="space-y-4">
                {statusSteps.map((step, index) => (
                  <View key={step.key} className="flex-row items-center">
                    <View className={`w-8 h-8 rounded-full items-center justify-center ${
                      step.completed 
                        ? 'bg-primary-500' 
                        : step.current 
                          ? 'bg-primary-200 border-2 border-primary-500'
                          : 'bg-neutral-200'
                    }`}>
                      <Ionicons 
                        name={step.completed ? 'checkmark' : step.icon as any} 
                        size={16} 
                        color={step.completed ? '#ffffff' : step.current ? '#22c55e' : '#94a3b8'} 
                      />
                    </View>
                    <Text className={`ml-3 font-medium ${
                      step.completed || step.current ? 'text-neutral-800' : 'text-neutral-500'
                    }`}>
                      {step.label}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Order Items */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4">
              Order Items ({order.items.length})
            </Text>
            <View className="space-y-4">
              {order.items.map((item, index) => (
                <View key={index} className="flex-row items-center">
                  <Image
                    source={{ uri: item.product.image }}
                    className="w-12 h-12 rounded-lg"
                  />
                  <View className="flex-1 ml-3">
                    <Text className="text-base font-semibold text-neutral-800">
                      {item.product.name}
                    </Text>
                    <Text className="text-sm text-neutral-600">
                      {item.product.unit} × {item.quantity}
                    </Text>
                  </View>
                  <Text className="text-base font-bold text-neutral-800">
                    {formatPrice(item.product.price * item.quantity)}
                  </Text>
                </View>
              ))}
            </View>
          </View>

          {/* Order Summary */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4">
              Order Summary
            </Text>
            <View className="space-y-3">
              <View className="flex-row justify-between">
                <Text className="text-base text-neutral-700">Subtotal</Text>
                <Text className="text-base font-semibold text-neutral-800">
                  {formatPrice(order.subtotal)}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-base text-neutral-700">Delivery Fee</Text>
                <Text className="text-base font-semibold text-neutral-800">
                  {order.deliveryFee === 0 ? 'FREE' : formatPrice(order.deliveryFee)}
                </Text>
              </View>
              {order.discount > 0 && (
                <View className="flex-row justify-between">
                  <Text className="text-base text-neutral-700">Discount</Text>
                  <Text className="text-base font-semibold text-green-600">
                    -{formatPrice(order.discount)}
                  </Text>
                </View>
              )}
              <View className="border-t border-neutral-200 pt-3">
                <View className="flex-row justify-between">
                  <Text className="text-lg font-bold text-neutral-800">Total</Text>
                  <Text className="text-lg font-bold text-neutral-800">
                    {formatPrice(order.total)}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Delivery Address */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-lg font-bold text-neutral-800 mb-4">
              Delivery Address
            </Text>
            <View className="flex-row items-start">
              <Ionicons name="location" size={20} color="#64748b" />
              <View className="ml-3 flex-1">
                <Text className="text-base font-semibold text-neutral-800">
                  {order.deliveryAddress.type.charAt(0).toUpperCase() + order.deliveryAddress.type.slice(1)}
                </Text>
                <Text className="text-sm text-neutral-600 mt-1">
                  {order.deliveryAddress.street}
                </Text>
                <Text className="text-sm text-neutral-600">
                  {order.deliveryAddress.city}, {order.deliveryAddress.state} {order.deliveryAddress.zipCode}
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View className="space-y-3">
            {order.status !== 'CANCELLED' && order.status !== 'DELIVERED' && (
              <Button
                title="Track Order"
                onPress={handleTrackOrder}
                size="lg"
                fullWidth
              />
            )}
            <Button
              title="Reorder Items"
              onPress={handleReorder}
              variant="outline"
              size="lg"
              fullWidth
            />
            <Button
              title="Contact Support"
              onPress={handleContactSupport}
              variant="outline"
              size="lg"
              fullWidth
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrderDetailsScreen;
