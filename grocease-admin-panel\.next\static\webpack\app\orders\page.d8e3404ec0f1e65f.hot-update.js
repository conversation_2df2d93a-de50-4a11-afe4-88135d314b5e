"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n\nclass ApiClient {\n    // Auth endpoints\n    async login(credentials) {\n        const response = await this.client.post(\"/auth/login\", credentials);\n        return response.data.data;\n    }\n    // Dashboard endpoints\n    async getDashboardOverview() {\n        const response = await this.client.get(\"/admin/dashboard/overview\");\n        return response.data.data;\n    }\n    // Analytics endpoints\n    async getMonthlySalesData() {\n        let months = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 12;\n        const response = await this.client.get(\"/analytics/sales/monthly?months=\".concat(months));\n        return response.data.data;\n    }\n    async getWeeklySalesData() {\n        let weeks = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 12;\n        const response = await this.client.get(\"/analytics/sales/weekly?weeks=\".concat(weeks));\n        return response.data.data;\n    }\n    async getMostSoldProducts() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        const response = await this.client.get(\"/analytics/products/most-sold?days=\".concat(days, \"&limit=\").concat(limit));\n        return response.data.data;\n    }\n    async getPopularCategories() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await this.client.get(\"/analytics/categories/popular?days=\".concat(days));\n        return response.data.data;\n    }\n    async getUserEngagementMetrics() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        const response = await this.client.get(\"/analytics/users/engagement?days=\".concat(days));\n        return response.data.data;\n    }\n    // Order endpoints\n    async getOrders() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0, status = arguments.length > 3 ? arguments[3] : void 0;\n        // Convert 0-based page to 1-based for backend\n        const backendPage = Math.max(0, page);\n        const params = new URLSearchParams({\n            page: backendPage.toString(),\n            limit: limit.toString()\n        });\n        if (search && search.trim()) {\n            params.append(\"search\", search.trim());\n        }\n        if (status && status !== \"ALL\") {\n            params.append(\"status\", status);\n        }\n        const response = await this.client.get(\"/admin/orders?\".concat(params));\n        return response.data;\n    }\n    async getOrder(id) {\n        const response = await this.client.get(\"/admin/orders/\".concat(id));\n        return response.data.data;\n    }\n    async updateOrderStatus(id, status, notes) {\n        const response = await this.client.put(\"/admin/orders/\".concat(id, \"/status\"), {\n            status,\n            notes\n        });\n        return response.data.data;\n    }\n    // User endpoints\n    async getUsers() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        const response = await this.client.get(\"/admin/users?page=\".concat(page, \"&limit=\").concat(limit));\n        return response.data;\n    }\n    async getUser(id) {\n        const response = await this.client.get(\"/admin/users/\".concat(id));\n        return response.data.data;\n    }\n    async updateProfile(profileData) {\n        const response = await this.client.put(\"/users/profile\", profileData);\n        return response.data.data;\n    }\n    // Product endpoints\n    async getProducts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, categoryId = arguments.length > 2 ? arguments[2] : void 0, search = arguments.length > 3 ? arguments[3] : void 0;\n        let url = \"/products?page=\".concat(page, \"&limit=\").concat(limit);\n        if (categoryId) url += \"&categoryId=\".concat(categoryId);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async getProduct(id) {\n        const response = await this.client.get(\"/products/\".concat(id));\n        return response.data.data;\n    }\n    async getCategories() {\n        const response = await this.client.get(\"/categories\");\n        return response.data.data;\n    }\n    async getBanners() {\n        const response = await this.client.get(\"/banners\");\n        return response.data.data;\n    }\n    async getFeaturedProducts() {\n        const response = await this.client.get(\"/products/featured\");\n        return response.data.data;\n    }\n    // Notification endpoints\n    async sendNotification(request) {\n        await this.client.post(\"/admin/notifications/send\", request);\n    }\n    async getNotificationHistory() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, userId = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/notifications/history?page=\".concat(page, \"&size=\").concat(size);\n        if (userId) url += \"&userId=\".concat(userId);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    // File upload endpoints\n    async uploadImage(file, type) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const response = await this.client.post(\"/upload/\".concat(type), formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        });\n        return response.data.data;\n    }\n    // Admin Product Management\n    async createProduct(productData) {\n        const response = await this.client.post(\"/admin/products\", productData);\n        return response.data.data;\n    }\n    async updateProduct(id, productData) {\n        const response = await this.client.put(\"/admin/products/\".concat(id), productData);\n        return response.data.data;\n    }\n    async deleteProduct(id) {\n        await this.client.delete(\"/admin/products/\".concat(id));\n    }\n    // Admin Category Management\n    async getAdminCategories() {\n        const response = await this.client.get(\"/admin/products/categories\");\n        return response.data.data;\n    }\n    async createCategory(categoryData) {\n        const response = await this.client.post(\"/admin/products/categories\", categoryData);\n        return response.data.data;\n    }\n    async updateCategory(id, categoryData) {\n        const response = await this.client.put(\"/admin/products/categories/\".concat(id), categoryData);\n        return response.data.data;\n    }\n    async deleteCategory(id) {\n        await this.client.delete(\"/admin/products/categories/\".concat(id));\n    }\n    // Admin Banner Management\n    async getBanners() {\n        const response = await this.client.get(\"/admin/banners\");\n        return response.data.data;\n    }\n    async getBanner(id) {\n        const response = await this.client.get(\"/admin/banners/\".concat(id));\n        return response.data.data;\n    }\n    async createBanner(bannerData) {\n        const response = await this.client.post(\"/admin/banners\", bannerData);\n        return response.data.data;\n    }\n    async updateBanner(id, bannerData) {\n        const response = await this.client.put(\"/admin/banners/\".concat(id), bannerData);\n        return response.data.data;\n    }\n    async deleteBanner(id) {\n        await this.client.delete(\"/admin/banners/\".concat(id));\n    }\n    async toggleBannerStatus(id) {\n        const response = await this.client.put(\"/admin/banners/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    // Admin User Management\n    async getAdminUsers() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/users?page=\".concat(page, \"&limit=\").concat(limit);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async toggleUserStatus(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    async verifyUserEmail(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/verify-email\"));\n        return response.data.data;\n    }\n    async verifyUserPhone(id) {\n        const response = await this.client.put(\"/admin/users/\".concat(id, \"/verify-phone\"));\n        return response.data.data;\n    }\n    async deleteUser(id) {\n        await this.client.delete(\"/admin/users/\".concat(id));\n    }\n    // Admin Discount Code Management\n    async getDiscountCodes() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 ? arguments[2] : void 0;\n        let url = \"/admin/discount-codes?page=\".concat(page, \"&limit=\").concat(limit);\n        if (search) url += \"&search=\".concat(search);\n        const response = await this.client.get(url);\n        return response.data;\n    }\n    async getDiscountCode(id) {\n        const response = await this.client.get(\"/admin/discount-codes/\".concat(id));\n        return response.data.data;\n    }\n    async createDiscountCode(discountCodeData) {\n        const response = await this.client.post(\"/admin/discount-codes\", discountCodeData);\n        return response.data.data;\n    }\n    async updateDiscountCode(id, discountCodeData) {\n        const response = await this.client.put(\"/admin/discount-codes/\".concat(id), discountCodeData);\n        return response.data.data;\n    }\n    async deleteDiscountCode(id) {\n        await this.client.delete(\"/admin/discount-codes/\".concat(id));\n    }\n    async toggleDiscountCodeStatus(id) {\n        const response = await this.client.post(\"/admin/discount-codes/\".concat(id, \"/toggle-status\"));\n        return response.data.data;\n    }\n    async applyDiscountCode(request) {\n        const response = await this.client.post(\"/discount-codes/apply\", request);\n        return response.data.data;\n    }\n    async validateDiscountCode(code) {\n        const response = await this.client.get(\"/discount-codes/validate/\".concat(code));\n        return response.data.data;\n    }\n    // Admin Product Featured Management\n    async toggleProductFeaturedStatus(id) {\n        const response = await this.client.put(\"/admin/products/\".concat(id, \"/toggle-featured\"));\n        return response.data.data;\n    }\n    constructor(){\n        this.client = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: \"http://localhost:8080/api\" || 0,\n            timeout: 10000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Request interceptor to add auth token\n        this.client.interceptors.request.use((config)=>{\n            const token = localStorage.getItem(\"admin_token\");\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            return config;\n        }, (error)=>Promise.reject(error));\n        // Response interceptor for error handling\n        this.client.interceptors.response.use((response)=>response, (error)=>{\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                localStorage.removeItem(\"admin_token\");\n                localStorage.removeItem(\"admin_user\");\n                window.location.href = \"/login\";\n            }\n            return Promise.reject(error);\n        });\n    }\n}\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEO0FBNEIzRCxNQUFNQztJQXNDSixpQkFBaUI7SUFDakIsTUFBTUMsTUFBTUMsV0FBeUIsRUFBeUI7UUFDNUQsTUFBTUMsV0FBcUQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGVBQWVIO1FBQ2pHLE9BQU9DLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNQyx1QkFBbUQ7UUFDdkQsTUFBTUosV0FBMEQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDO1FBQ3RGLE9BQU9MLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLHNCQUFzQjtJQUN0QixNQUFNRyxzQkFBK0Q7WUFBM0NDLFNBQUFBLGlFQUFpQjtRQUN6QyxNQUFNUCxXQUFvRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDSSxHQUFHLENBQUMsbUNBQTBDLE9BQVBFO1FBQ25ILE9BQU9QLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1LLHFCQUE2RDtZQUExQ0MsUUFBQUEsaUVBQWdCO1FBQ3ZDLE1BQU1ULFdBQW9ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxpQ0FBdUMsT0FBTkk7UUFDakgsT0FBT1QsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTU8sc0JBQW9GO1lBQWhFQyxPQUFBQSxpRUFBZSxJQUFJQyxRQUFBQSxpRUFBZ0I7UUFDM0QsTUFBTVosV0FBdUQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLHNDQUFvRE8sT0FBZEQsTUFBSyxXQUFlLE9BQU5DO1FBQ3ZJLE9BQU9aLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1VLHVCQUFrRTtZQUE3Q0YsT0FBQUEsaUVBQWU7UUFDeEMsTUFBTVgsV0FBd0QsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLHNDQUEyQyxPQUFMTTtRQUMxSCxPQUFPWCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNVywyQkFBdUU7WUFBOUNILE9BQUFBLGlFQUFlO1FBQzVDLE1BQU1YLFdBQXlELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxvQ0FBeUMsT0FBTE07UUFDekgsT0FBT1gsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1ZLFlBQXFIO1lBQTNHQyxPQUFBQSxpRUFBZSxHQUFHSixRQUFBQSxpRUFBZ0IsSUFBSUssdURBQWlCQztRQUNyRSw4Q0FBOEM7UUFDOUMsTUFBTUMsY0FBY0MsS0FBS0MsR0FBRyxDQUFDLEdBQUdMO1FBRWhDLE1BQU1NLFNBQVMsSUFBSUMsZ0JBQWdCO1lBQ2pDUCxNQUFNRyxZQUFZSyxRQUFRO1lBQzFCWixPQUFPQSxNQUFNWSxRQUFRO1FBQ3ZCO1FBRUEsSUFBSVAsVUFBVUEsT0FBT1EsSUFBSSxJQUFJO1lBQzNCSCxPQUFPSSxNQUFNLENBQUMsVUFBVVQsT0FBT1EsSUFBSTtRQUNyQztRQUVBLElBQUlQLFVBQVVBLFdBQVcsT0FBTztZQUM5QkksT0FBT0ksTUFBTSxDQUFDLFVBQVVSO1FBQzFCO1FBRUEsTUFBTWxCLFdBQW9ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxpQkFBd0IsT0FBUGlCO1FBQ2pHLE9BQU90QixTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTXdCLFNBQVNDLEVBQVUsRUFBa0I7UUFDekMsTUFBTTVCLFdBQThDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxpQkFBb0IsT0FBSHVCO1FBQzNGLE9BQU81QixTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNMEIsa0JBQWtCRCxFQUFVLEVBQUVWLE1BQW1CLEVBQUVZLEtBQWMsRUFBa0I7UUFDdkYsTUFBTTlCLFdBQThDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUM4QixHQUFHLENBQUMsaUJBQW9CLE9BQUhILElBQUcsWUFBVTtZQUFFVjtZQUFRWTtRQUFNO1FBQ3hILE9BQU85QixTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxpQkFBaUI7SUFDakIsTUFBTTZCLFdBQWlGO1lBQXhFaEIsT0FBQUEsaUVBQWUsR0FBR0osUUFBQUEsaUVBQWdCO1FBQy9DLE1BQU1aLFdBQW1ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxxQkFBbUNPLE9BQWRJLE1BQUssV0FBZSxPQUFOSjtRQUNsSCxPQUFPWixTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTThCLFFBQVFMLEVBQVUsRUFBaUI7UUFDdkMsTUFBTTVCLFdBQTZDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxnQkFBbUIsT0FBSHVCO1FBQ3pGLE9BQU81QixTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNK0IsY0FBY0MsV0FBMEIsRUFBaUI7UUFDN0QsTUFBTW5DLFdBQTZDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUM4QixHQUFHLENBQUMsa0JBQWtCSTtRQUMzRixPQUFPbkMsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU1pQyxjQUE2SDtZQUFqSHBCLE9BQUFBLGlFQUFlLEdBQUdKLFFBQUFBLGlFQUFnQixJQUFJeUIsMkRBQXFCcEI7UUFDM0UsSUFBSXFCLE1BQU0sa0JBQWdDMUIsT0FBZEksTUFBSyxXQUFlLE9BQU5KO1FBQzFDLElBQUl5QixZQUFZQyxPQUFPLGVBQTBCLE9BQVhEO1FBQ3RDLElBQUlwQixRQUFRcUIsT0FBTyxXQUFrQixPQUFQckI7UUFFOUIsTUFBTWpCLFdBQXNELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQ2lDO1FBQ2xGLE9BQU90QyxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTW9DLFdBQVdYLEVBQVUsRUFBb0I7UUFDN0MsTUFBTTVCLFdBQWdELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQyxhQUFnQixPQUFIdUI7UUFDekYsT0FBTzVCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1xQyxnQkFBcUM7UUFDekMsTUFBTXhDLFdBQW1ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQztRQUMvRSxPQUFPTCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNc0MsYUFBZ0M7UUFDcEMsTUFBTXpDLFdBQWlELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQztRQUM3RSxPQUFPTCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNdUMsc0JBQTBDO1FBQzlDLE1BQU0xQyxXQUFrRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDSSxHQUFHLENBQUM7UUFDOUUsT0FBT0wsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU13QyxpQkFBaUJDLE9BQWdDLEVBQWlCO1FBQ3RFLE1BQU0sSUFBSSxDQUFDM0MsTUFBTSxDQUFDQyxJQUFJLENBQUMsNkJBQTZCMEM7SUFDdEQ7SUFFQSxNQUFNQyx5QkFBOEg7WUFBdkc3QixPQUFBQSxpRUFBZSxHQUFHOEIsT0FBQUEsaUVBQWUsSUFBSUM7UUFDaEUsSUFBSVQsTUFBTSxxQ0FBa0RRLE9BQWI5QixNQUFLLFVBQWEsT0FBTDhCO1FBQzVELElBQUlDLFFBQVFULE9BQU8sV0FBa0IsT0FBUFM7UUFFOUIsTUFBTS9DLFdBQWtFLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQ2lDO1FBQzlGLE9BQU90QyxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU02QyxZQUFZQyxJQUFVLEVBQUVDLElBQWtELEVBQWlDO1FBQy9HLE1BQU1DLFdBQVcsSUFBSUM7UUFDckJELFNBQVN6QixNQUFNLENBQUMsUUFBUXVCO1FBRXhCLE1BQU1qRCxXQUE2RCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxJQUFJLENBQUMsV0FBZ0IsT0FBTGdELE9BQVFDLFVBQVU7WUFDckhFLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFDQSxPQUFPckQsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1tRCxjQUFjQyxXQUFpQyxFQUFvQjtRQUN2RSxNQUFNdkQsV0FBZ0QsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLG1CQUFtQnFEO1FBQ2hHLE9BQU92RCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNcUQsY0FBYzVCLEVBQVUsRUFBRTJCLFdBQWlDLEVBQW9CO1FBQ25GLE1BQU12RCxXQUFnRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDOEIsR0FBRyxDQUFDLG1CQUFzQixPQUFISCxLQUFNMkI7UUFDckcsT0FBT3ZELFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1zRCxjQUFjN0IsRUFBVSxFQUFpQjtRQUM3QyxNQUFNLElBQUksQ0FBQzNCLE1BQU0sQ0FBQ3lELE1BQU0sQ0FBQyxtQkFBc0IsT0FBSDlCO0lBQzlDO0lBRUEsNEJBQTRCO0lBQzVCLE1BQU0rQixxQkFBMEM7UUFDOUMsTUFBTTNELFdBQW1ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQztRQUMvRSxPQUFPTCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNeUQsZUFBZUMsWUFBbUMsRUFBcUI7UUFDM0UsTUFBTTdELFdBQWlELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNDLElBQUksQ0FBQyw4QkFBOEIyRDtRQUM1RyxPQUFPN0QsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTTJELGVBQWVsQyxFQUFVLEVBQUVpQyxZQUFtQyxFQUFxQjtRQUN2RixNQUFNN0QsV0FBaUQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQzhCLEdBQUcsQ0FBQyw4QkFBaUMsT0FBSEgsS0FBTWlDO1FBQ2pILE9BQU83RCxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNNEQsZUFBZW5DLEVBQVUsRUFBaUI7UUFDOUMsTUFBTSxJQUFJLENBQUMzQixNQUFNLENBQUN5RCxNQUFNLENBQUMsOEJBQWlDLE9BQUg5QjtJQUN6RDtJQUVBLDBCQUEwQjtJQUMxQixNQUFNYSxhQUFnQztRQUNwQyxNQUFNekMsV0FBaUQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDO1FBQzdFLE9BQU9MLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU02RCxVQUFVcEMsRUFBVSxFQUFtQjtRQUMzQyxNQUFNNUIsV0FBK0MsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLGtCQUFxQixPQUFIdUI7UUFDN0YsT0FBTzVCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU04RCxhQUFhQyxVQUErQixFQUFtQjtRQUNuRSxNQUFNbEUsV0FBK0MsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLGtCQUFrQmdFO1FBQzlGLE9BQU9sRSxTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNZ0UsYUFBYXZDLEVBQVUsRUFBRXNDLFVBQStCLEVBQW1CO1FBQy9FLE1BQU1sRSxXQUErQyxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDOEIsR0FBRyxDQUFDLGtCQUFxQixPQUFISCxLQUFNc0M7UUFDbkcsT0FBT2xFLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1pRSxhQUFheEMsRUFBVSxFQUFpQjtRQUM1QyxNQUFNLElBQUksQ0FBQzNCLE1BQU0sQ0FBQ3lELE1BQU0sQ0FBQyxrQkFBcUIsT0FBSDlCO0lBQzdDO0lBRUEsTUFBTXlDLG1CQUFtQnpDLEVBQVUsRUFBbUI7UUFDcEQsTUFBTTVCLFdBQStDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUM4QixHQUFHLENBQUMsa0JBQXFCLE9BQUhILElBQUc7UUFDaEcsT0FBTzVCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNbUUsZ0JBQXVHO1lBQXpGdEQsT0FBQUEsaUVBQWUsR0FBR0osUUFBQUEsaUVBQWdCLElBQUlLO1FBQ3hELElBQUlxQixNQUFNLHFCQUFtQzFCLE9BQWRJLE1BQUssV0FBZSxPQUFOSjtRQUM3QyxJQUFJSyxRQUFRcUIsT0FBTyxXQUFrQixPQUFQckI7UUFFOUIsTUFBTWpCLFdBQW1ELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNJLEdBQUcsQ0FBQ2lDO1FBQy9FLE9BQU90QyxTQUFTRyxJQUFJO0lBQ3RCO0lBRUEsTUFBTW9FLGlCQUFpQjNDLEVBQVUsRUFBaUI7UUFDaEQsTUFBTTVCLFdBQTZDLE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUM4QixHQUFHLENBQUMsZ0JBQW1CLE9BQUhILElBQUc7UUFDNUYsT0FBTzVCLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU1xRSxnQkFBZ0I1QyxFQUFVLEVBQWlCO1FBQy9DLE1BQU01QixXQUE2QyxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDOEIsR0FBRyxDQUFDLGdCQUFtQixPQUFISCxJQUFHO1FBQzVGLE9BQU81QixTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNc0UsZ0JBQWdCN0MsRUFBVSxFQUFpQjtRQUMvQyxNQUFNNUIsV0FBNkMsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQzhCLEdBQUcsQ0FBQyxnQkFBbUIsT0FBSEgsSUFBRztRQUM1RixPQUFPNUIsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTXVFLFdBQVc5QyxFQUFVLEVBQWlCO1FBQzFDLE1BQU0sSUFBSSxDQUFDM0IsTUFBTSxDQUFDeUQsTUFBTSxDQUFDLGdCQUFtQixPQUFIOUI7SUFDM0M7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTStDLG1CQUFrSDtZQUFqRzNELE9BQUFBLGlFQUFlLEdBQUdKLFFBQUFBLGlFQUFnQixJQUFJSztRQUMzRCxJQUFJcUIsTUFBTSw4QkFBNEMxQixPQUFkSSxNQUFLLFdBQWUsT0FBTko7UUFDdEQsSUFBSUssUUFBUXFCLE9BQU8sV0FBa0IsT0FBUHJCO1FBRTlCLE1BQU1qQixXQUEyRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDSSxHQUFHLENBQUNpQztRQUN2RixPQUFPdEMsU0FBU0csSUFBSTtJQUN0QjtJQUVBLE1BQU15RSxnQkFBZ0JoRCxFQUFVLEVBQXlCO1FBQ3ZELE1BQU01QixXQUFxRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDSSxHQUFHLENBQUMseUJBQTRCLE9BQUh1QjtRQUMxRyxPQUFPNUIsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTTBFLG1CQUFtQkMsZ0JBQTJDLEVBQXlCO1FBQzNGLE1BQU05RSxXQUFxRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxJQUFJLENBQUMseUJBQXlCNEU7UUFDM0csT0FBTzlFLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLE1BQU00RSxtQkFBbUJuRCxFQUFVLEVBQUVrRCxnQkFBMkMsRUFBeUI7UUFDdkcsTUFBTTlFLFdBQXFELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUM4QixHQUFHLENBQUMseUJBQTRCLE9BQUhILEtBQU1rRDtRQUNoSCxPQUFPOUUsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTTZFLG1CQUFtQnBELEVBQVUsRUFBaUI7UUFDbEQsTUFBTSxJQUFJLENBQUMzQixNQUFNLENBQUN5RCxNQUFNLENBQUMseUJBQTRCLE9BQUg5QjtJQUNwRDtJQUVBLE1BQU1xRCx5QkFBeUJyRCxFQUFVLEVBQXlCO1FBQ2hFLE1BQU01QixXQUFxRCxNQUFNLElBQUksQ0FBQ0MsTUFBTSxDQUFDQyxJQUFJLENBQUMseUJBQTRCLE9BQUgwQixJQUFHO1FBQzlHLE9BQU81QixTQUFTRyxJQUFJLENBQUNBLElBQUk7SUFDM0I7SUFFQSxNQUFNK0Usa0JBQWtCdEMsT0FBNkIsRUFBMkI7UUFDOUUsTUFBTTVDLFdBQXVELE1BQU0sSUFBSSxDQUFDQyxNQUFNLENBQUNDLElBQUksQ0FBQyx5QkFBeUIwQztRQUM3RyxPQUFPNUMsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEsTUFBTWdGLHFCQUFxQkMsSUFBWSxFQUF5QjtRQUM5RCxNQUFNcEYsV0FBcUQsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0ksR0FBRyxDQUFDLDRCQUFpQyxPQUFMK0U7UUFDN0csT0FBT3BGLFNBQVNHLElBQUksQ0FBQ0EsSUFBSTtJQUMzQjtJQUVBLG9DQUFvQztJQUNwQyxNQUFNa0YsNEJBQTRCekQsRUFBVSxFQUFvQjtRQUM5RCxNQUFNNUIsV0FBZ0QsTUFBTSxJQUFJLENBQUNDLE1BQU0sQ0FBQzhCLEdBQUcsQ0FBQyxtQkFBc0IsT0FBSEgsSUFBRztRQUNsRyxPQUFPNUIsU0FBU0csSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBN1RBbUYsYUFBYztRQUNaLElBQUksQ0FBQ3JGLE1BQU0sR0FBR0wsNkNBQUtBLENBQUMyRixNQUFNLENBQUM7WUFDekJDLFNBQVNDLDJCQUErQixJQUFJO1lBQzVDRyxTQUFTO1lBQ1R2QyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsd0NBQXdDO1FBQ3hDLElBQUksQ0FBQ3BELE1BQU0sQ0FBQzRGLFlBQVksQ0FBQ2pELE9BQU8sQ0FBQ2tELEdBQUcsQ0FDbEMsQ0FBQ0M7WUFDQyxNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMsSUFBSUYsT0FBTztnQkFDVEQsT0FBTzFDLE9BQU8sQ0FBQzhDLGFBQWEsR0FBRyxVQUFnQixPQUFOSDtZQUMzQztZQUNBLE9BQU9EO1FBQ1QsR0FDQSxDQUFDSyxRQUFVQyxRQUFRQyxNQUFNLENBQUNGO1FBRzVCLDBDQUEwQztRQUMxQyxJQUFJLENBQUNuRyxNQUFNLENBQUM0RixZQUFZLENBQUM3RixRQUFRLENBQUM4RixHQUFHLENBQ25DLENBQUM5RixXQUFhQSxVQUNkLENBQUNvRztnQkFDS0E7WUFBSixJQUFJQSxFQUFBQSxrQkFBQUEsTUFBTXBHLFFBQVEsY0FBZG9HLHNDQUFBQSxnQkFBZ0JsRixNQUFNLE1BQUssS0FBSztnQkFDbEMrRSxhQUFhTSxVQUFVLENBQUM7Z0JBQ3hCTixhQUFhTSxVQUFVLENBQUM7Z0JBQ3hCQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztZQUN6QjtZQUNBLE9BQU9MLFFBQVFDLE1BQU0sQ0FBQ0Y7UUFDeEI7SUFFSjtBQTZSRjtBQUVPLE1BQU1PLFlBQVksSUFBSTlHLFlBQVc7QUFDeEMsK0RBQWU4RyxTQUFTQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvYXBpLnRzPzJmYWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zLCB7IEF4aW9zSW5zdGFuY2UsIEF4aW9zUmVzcG9uc2UgfSBmcm9tICdheGlvcydcbmltcG9ydCB7XG4gIEFwaVJlc3BvbnNlLFxuICBQYWdpbmF0ZWRSZXNwb25zZSxcbiAgTG9naW5SZXF1ZXN0LFxuICBBdXRoUmVzcG9uc2UsXG4gIERhc2hib2FyZE92ZXJ2aWV3LFxuICBTYWxlc0RhdGEsXG4gIFByb2R1Y3RTYWxlcyxcbiAgQ2F0ZWdvcnlTYWxlcyxcbiAgVXNlckVuZ2FnZW1lbnQsXG4gIE9yZGVyLFxuICBPcmRlclN0YXR1cyxcbiAgVXNlcixcbiAgUHJvZHVjdCxcbiAgQ2F0ZWdvcnksXG4gIEJhbm5lcixcbiAgTm90aWZpY2F0aW9uSGlzdG9yeSxcbiAgU2VuZE5vdGlmaWNhdGlvblJlcXVlc3QsXG4gIERpc2NvdW50Q29kZSxcbiAgQ3JlYXRlRGlzY291bnRDb2RlUmVxdWVzdCxcbiAgQXBwbHlEaXNjb3VudFJlcXVlc3QsXG4gIERpc2NvdW50UmVzdWx0LFxuICBDcmVhdGVQcm9kdWN0UmVxdWVzdCxcbiAgQ3JlYXRlQ2F0ZWdvcnlSZXF1ZXN0LFxuICBDcmVhdGVCYW5uZXJSZXF1ZXN0XG59IGZyb20gJ0AvdHlwZXMnXG5cbmNsYXNzIEFwaUNsaWVudCB7XG4gIHByaXZhdGUgY2xpZW50OiBBeGlvc0luc3RhbmNlXG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5jbGllbnQgPSBheGlvcy5jcmVhdGUoe1xuICAgICAgYmFzZVVSTDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDgwL2FwaScsXG4gICAgICB0aW1lb3V0OiAxMDAwMCxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIC8vIFJlcXVlc3QgaW50ZXJjZXB0b3IgdG8gYWRkIGF1dGggdG9rZW5cbiAgICB0aGlzLmNsaWVudC5pbnRlcmNlcHRvcnMucmVxdWVzdC51c2UoXG4gICAgICAoY29uZmlnKSA9PiB7XG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWluX3Rva2VuJylcbiAgICAgICAgaWYgKHRva2VuKSB7XG4gICAgICAgICAgY29uZmlnLmhlYWRlcnMuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbmZpZ1xuICAgICAgfSxcbiAgICAgIChlcnJvcikgPT4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXG4gICAgKVxuXG4gICAgLy8gUmVzcG9uc2UgaW50ZXJjZXB0b3IgZm9yIGVycm9yIGhhbmRsaW5nXG4gICAgdGhpcy5jbGllbnQuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcbiAgICAgIChyZXNwb25zZSkgPT4gcmVzcG9uc2UsXG4gICAgICAoZXJyb3IpID0+IHtcbiAgICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbl90b2tlbicpXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FkbWluX3VzZXInKVxuICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbidcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXG4gICAgICB9XG4gICAgKVxuICB9XG5cbiAgLy8gQXV0aCBlbmRwb2ludHNcbiAgYXN5bmMgbG9naW4oY3JlZGVudGlhbHM6IExvZ2luUmVxdWVzdCk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8QXV0aFJlc3BvbnNlPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wb3N0KCcvYXV0aC9sb2dpbicsIGNyZWRlbnRpYWxzKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIC8vIERhc2hib2FyZCBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0RGFzaGJvYXJkT3ZlcnZpZXcoKTogUHJvbWlzZTxEYXNoYm9hcmRPdmVydmlldz4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPERhc2hib2FyZE92ZXJ2aWV3Pj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoJy9hZG1pbi9kYXNoYm9hcmQvb3ZlcnZpZXcnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIC8vIEFuYWx5dGljcyBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0TW9udGhseVNhbGVzRGF0YShtb250aHM6IG51bWJlciA9IDEyKTogUHJvbWlzZTxTYWxlc0RhdGFbXT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFNhbGVzRGF0YVtdPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9hbmFseXRpY3Mvc2FsZXMvbW9udGhseT9tb250aHM9JHttb250aHN9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBnZXRXZWVrbHlTYWxlc0RhdGEod2Vla3M6IG51bWJlciA9IDEyKTogUHJvbWlzZTxTYWxlc0RhdGFbXT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFNhbGVzRGF0YVtdPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9hbmFseXRpY3Mvc2FsZXMvd2Vla2x5P3dlZWtzPSR7d2Vla3N9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBnZXRNb3N0U29sZFByb2R1Y3RzKGRheXM6IG51bWJlciA9IDMwLCBsaW1pdDogbnVtYmVyID0gMTApOiBQcm9taXNlPFByb2R1Y3RTYWxlc1tdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8UHJvZHVjdFNhbGVzW10+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2FuYWx5dGljcy9wcm9kdWN0cy9tb3N0LXNvbGQ/ZGF5cz0ke2RheXN9JmxpbWl0PSR7bGltaXR9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBnZXRQb3B1bGFyQ2F0ZWdvcmllcyhkYXlzOiBudW1iZXIgPSAzMCk6IFByb21pc2U8Q2F0ZWdvcnlTYWxlc1tdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8Q2F0ZWdvcnlTYWxlc1tdPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9hbmFseXRpY3MvY2F0ZWdvcmllcy9wb3B1bGFyP2RheXM9JHtkYXlzfWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgZ2V0VXNlckVuZ2FnZW1lbnRNZXRyaWNzKGRheXM6IG51bWJlciA9IDMwKTogUHJvbWlzZTxVc2VyRW5nYWdlbWVudFtdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8VXNlckVuZ2FnZW1lbnRbXT4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvYW5hbHl0aWNzL3VzZXJzL2VuZ2FnZW1lbnQ/ZGF5cz0ke2RheXN9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICAvLyBPcmRlciBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0T3JkZXJzKHBhZ2U6IG51bWJlciA9IDAsIGxpbWl0OiBudW1iZXIgPSAxMCwgc2VhcmNoPzogc3RyaW5nLCBzdGF0dXM/OiBzdHJpbmcpOiBQcm9taXNlPFBhZ2luYXRlZFJlc3BvbnNlPE9yZGVyPj4ge1xuICAgIC8vIENvbnZlcnQgMC1iYXNlZCBwYWdlIHRvIDEtYmFzZWQgZm9yIGJhY2tlbmRcbiAgICBjb25zdCBiYWNrZW5kUGFnZSA9IE1hdGgubWF4KDAsIHBhZ2UpXG5cbiAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHtcbiAgICAgIHBhZ2U6IGJhY2tlbmRQYWdlLnRvU3RyaW5nKCksXG4gICAgICBsaW1pdDogbGltaXQudG9TdHJpbmcoKVxuICAgIH0pXG5cbiAgICBpZiAoc2VhcmNoICYmIHNlYXJjaC50cmltKCkpIHtcbiAgICAgIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaC50cmltKCkpXG4gICAgfVxuXG4gICAgaWYgKHN0YXR1cyAmJiBzdGF0dXMgIT09ICdBTEwnKSB7XG4gICAgICBwYXJhbXMuYXBwZW5kKCdzdGF0dXMnLCBzdGF0dXMpXG4gICAgfVxuXG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8UGFnaW5hdGVkUmVzcG9uc2U8T3JkZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2FkbWluL29yZGVycz8ke3BhcmFtc31gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH1cblxuICBhc3luYyBnZXRPcmRlcihpZDogc3RyaW5nKTogUHJvbWlzZTxPcmRlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPE9yZGVyPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9hZG1pbi9vcmRlcnMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZU9yZGVyU3RhdHVzKGlkOiBzdHJpbmcsIHN0YXR1czogT3JkZXJTdGF0dXMsIG5vdGVzPzogc3RyaW5nKTogUHJvbWlzZTxPcmRlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPE9yZGVyPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wdXQoYC9hZG1pbi9vcmRlcnMvJHtpZH0vc3RhdHVzYCwgeyBzdGF0dXMsIG5vdGVzIH0pXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgLy8gVXNlciBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0VXNlcnMocGFnZTogbnVtYmVyID0gMCwgbGltaXQ6IG51bWJlciA9IDEwKTogUHJvbWlzZTxQYWdpbmF0ZWRSZXNwb25zZTxVc2VyPj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPFBhZ2luYXRlZFJlc3BvbnNlPFVzZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2FkbWluL3VzZXJzP3BhZ2U9JHtwYWdlfSZsaW1pdD0ke2xpbWl0fWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGdldFVzZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFVzZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2FkbWluL3VzZXJzLyR7aWR9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyB1cGRhdGVQcm9maWxlKHByb2ZpbGVEYXRhOiBQYXJ0aWFsPFVzZXI+KTogUHJvbWlzZTxVc2VyPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8VXNlcj4+ID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KCcvdXNlcnMvcHJvZmlsZScsIHByb2ZpbGVEYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIC8vIFByb2R1Y3QgZW5kcG9pbnRzXG4gIGFzeW5jIGdldFByb2R1Y3RzKHBhZ2U6IG51bWJlciA9IDAsIGxpbWl0OiBudW1iZXIgPSAxMCwgY2F0ZWdvcnlJZD86IHN0cmluZywgc2VhcmNoPzogc3RyaW5nKTogUHJvbWlzZTxQYWdpbmF0ZWRSZXNwb25zZTxQcm9kdWN0Pj4ge1xuICAgIGxldCB1cmwgPSBgL3Byb2R1Y3RzP3BhZ2U9JHtwYWdlfSZsaW1pdD0ke2xpbWl0fWBcbiAgICBpZiAoY2F0ZWdvcnlJZCkgdXJsICs9IGAmY2F0ZWdvcnlJZD0ke2NhdGVnb3J5SWR9YFxuICAgIGlmIChzZWFyY2gpIHVybCArPSBgJnNlYXJjaD0ke3NlYXJjaH1gXG4gICAgXG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8UGFnaW5hdGVkUmVzcG9uc2U8UHJvZHVjdD4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KHVybClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9XG5cbiAgYXN5bmMgZ2V0UHJvZHVjdChpZDogc3RyaW5nKTogUHJvbWlzZTxQcm9kdWN0PiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8UHJvZHVjdD4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvcHJvZHVjdHMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGdldENhdGVnb3JpZXMoKTogUHJvbWlzZTxDYXRlZ29yeVtdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8Q2F0ZWdvcnlbXT4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KCcvY2F0ZWdvcmllcycpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgZ2V0QmFubmVycygpOiBQcm9taXNlPEJhbm5lcltdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8QmFubmVyW10+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCgnL2Jhbm5lcnMnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGdldEZlYXR1cmVkUHJvZHVjdHMoKTogUHJvbWlzZTxQcm9kdWN0W10+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxQcm9kdWN0W10+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCgnL3Byb2R1Y3RzL2ZlYXR1cmVkJylcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICAvLyBOb3RpZmljYXRpb24gZW5kcG9pbnRzXG4gIGFzeW5jIHNlbmROb3RpZmljYXRpb24ocmVxdWVzdDogU2VuZE5vdGlmaWNhdGlvblJlcXVlc3QpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBhd2FpdCB0aGlzLmNsaWVudC5wb3N0KCcvYWRtaW4vbm90aWZpY2F0aW9ucy9zZW5kJywgcmVxdWVzdClcbiAgfVxuXG4gIGFzeW5jIGdldE5vdGlmaWNhdGlvbkhpc3RvcnkocGFnZTogbnVtYmVyID0gMCwgc2l6ZTogbnVtYmVyID0gMjAsIHVzZXJJZD86IHN0cmluZyk6IFByb21pc2U8UGFnaW5hdGVkUmVzcG9uc2U8Tm90aWZpY2F0aW9uSGlzdG9yeT4+IHtcbiAgICBsZXQgdXJsID0gYC9hZG1pbi9ub3RpZmljYXRpb25zL2hpc3Rvcnk/cGFnZT0ke3BhZ2V9JnNpemU9JHtzaXplfWBcbiAgICBpZiAodXNlcklkKSB1cmwgKz0gYCZ1c2VySWQ9JHt1c2VySWR9YFxuICAgIFxuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPFBhZ2luYXRlZFJlc3BvbnNlPE5vdGlmaWNhdGlvbkhpc3Rvcnk+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCh1cmwpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfVxuXG4gIC8vIEZpbGUgdXBsb2FkIGVuZHBvaW50c1xuICBhc3luYyB1cGxvYWRJbWFnZShmaWxlOiBGaWxlLCB0eXBlOiAnYXZhdGFyJyB8ICdwcm9kdWN0JyB8ICdjYXRlZ29yeScgfCAnYmFubmVyJyk6IFByb21pc2U8eyBpbWFnZVVybDogc3RyaW5nIH0+IHtcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpXG4gICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZSlcblxuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPHsgaW1hZ2VVcmw6IHN0cmluZyB9Pj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wb3N0KGAvdXBsb2FkLyR7dHlwZX1gLCBmb3JtRGF0YSwge1xuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnLFxuICAgICAgfSxcbiAgICB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIC8vIEFkbWluIFByb2R1Y3QgTWFuYWdlbWVudFxuICBhc3luYyBjcmVhdGVQcm9kdWN0KHByb2R1Y3REYXRhOiBDcmVhdGVQcm9kdWN0UmVxdWVzdCk6IFByb21pc2U8UHJvZHVjdD4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFByb2R1Y3Q+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoJy9hZG1pbi9wcm9kdWN0cycsIHByb2R1Y3REYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZVByb2R1Y3QoaWQ6IHN0cmluZywgcHJvZHVjdERhdGE6IENyZWF0ZVByb2R1Y3RSZXF1ZXN0KTogUHJvbWlzZTxQcm9kdWN0PiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8UHJvZHVjdD4+ID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KGAvYWRtaW4vcHJvZHVjdHMvJHtpZH1gLCBwcm9kdWN0RGF0YSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBkZWxldGVQcm9kdWN0KGlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBhd2FpdCB0aGlzLmNsaWVudC5kZWxldGUoYC9hZG1pbi9wcm9kdWN0cy8ke2lkfWApXG4gIH1cblxuICAvLyBBZG1pbiBDYXRlZ29yeSBNYW5hZ2VtZW50XG4gIGFzeW5jIGdldEFkbWluQ2F0ZWdvcmllcygpOiBQcm9taXNlPENhdGVnb3J5W10+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxDYXRlZ29yeVtdPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoJy9hZG1pbi9wcm9kdWN0cy9jYXRlZ29yaWVzJylcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBjcmVhdGVDYXRlZ29yeShjYXRlZ29yeURhdGE6IENyZWF0ZUNhdGVnb3J5UmVxdWVzdCk6IFByb21pc2U8Q2F0ZWdvcnk+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxDYXRlZ29yeT4+ID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdCgnL2FkbWluL3Byb2R1Y3RzL2NhdGVnb3JpZXMnLCBjYXRlZ29yeURhdGEpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgdXBkYXRlQ2F0ZWdvcnkoaWQ6IHN0cmluZywgY2F0ZWdvcnlEYXRhOiBDcmVhdGVDYXRlZ29yeVJlcXVlc3QpOiBQcm9taXNlPENhdGVnb3J5PiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8Q2F0ZWdvcnk+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnB1dChgL2FkbWluL3Byb2R1Y3RzL2NhdGVnb3JpZXMvJHtpZH1gLCBjYXRlZ29yeURhdGEpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgZGVsZXRlQ2F0ZWdvcnkoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGF3YWl0IHRoaXMuY2xpZW50LmRlbGV0ZShgL2FkbWluL3Byb2R1Y3RzL2NhdGVnb3JpZXMvJHtpZH1gKVxuICB9XG5cbiAgLy8gQWRtaW4gQmFubmVyIE1hbmFnZW1lbnRcbiAgYXN5bmMgZ2V0QmFubmVycygpOiBQcm9taXNlPEJhbm5lcltdPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8QmFubmVyW10+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCgnL2FkbWluL2Jhbm5lcnMnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGdldEJhbm5lcihpZDogc3RyaW5nKTogUHJvbWlzZTxCYW5uZXI+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxCYW5uZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldChgL2FkbWluL2Jhbm5lcnMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGNyZWF0ZUJhbm5lcihiYW5uZXJEYXRhOiBDcmVhdGVCYW5uZXJSZXF1ZXN0KTogUHJvbWlzZTxCYW5uZXI+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxCYW5uZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoJy9hZG1pbi9iYW5uZXJzJywgYmFubmVyRGF0YSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyB1cGRhdGVCYW5uZXIoaWQ6IHN0cmluZywgYmFubmVyRGF0YTogQ3JlYXRlQmFubmVyUmVxdWVzdCk6IFByb21pc2U8QmFubmVyPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8QmFubmVyPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wdXQoYC9hZG1pbi9iYW5uZXJzLyR7aWR9YCwgYmFubmVyRGF0YSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyBkZWxldGVCYW5uZXIoaWQ6IHN0cmluZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGF3YWl0IHRoaXMuY2xpZW50LmRlbGV0ZShgL2FkbWluL2Jhbm5lcnMvJHtpZH1gKVxuICB9XG5cbiAgYXN5bmMgdG9nZ2xlQmFubmVyU3RhdHVzKGlkOiBzdHJpbmcpOiBQcm9taXNlPEJhbm5lcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPEJhbm5lcj4+ID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KGAvYWRtaW4vYmFubmVycy8ke2lkfS90b2dnbGUtc3RhdHVzYClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICAvLyBBZG1pbiBVc2VyIE1hbmFnZW1lbnRcbiAgYXN5bmMgZ2V0QWRtaW5Vc2VycyhwYWdlOiBudW1iZXIgPSAwLCBsaW1pdDogbnVtYmVyID0gMTAsIHNlYXJjaD86IHN0cmluZyk6IFByb21pc2U8UGFnaW5hdGVkUmVzcG9uc2U8VXNlcj4+IHtcbiAgICBsZXQgdXJsID0gYC9hZG1pbi91c2Vycz9wYWdlPSR7cGFnZX0mbGltaXQ9JHtsaW1pdH1gXG4gICAgaWYgKHNlYXJjaCkgdXJsICs9IGAmc2VhcmNoPSR7c2VhcmNofWBcblxuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPFBhZ2luYXRlZFJlc3BvbnNlPFVzZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LmdldCh1cmwpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfVxuXG4gIGFzeW5jIHRvZ2dsZVVzZXJTdGF0dXMoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFVzZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnB1dChgL2FkbWluL3VzZXJzLyR7aWR9L3RvZ2dsZS1zdGF0dXNgKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIHZlcmlmeVVzZXJFbWFpbChpZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8VXNlcj4+ID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KGAvYWRtaW4vdXNlcnMvJHtpZH0vdmVyaWZ5LWVtYWlsYClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICBhc3luYyB2ZXJpZnlVc2VyUGhvbmUoaWQ6IHN0cmluZyk6IFByb21pc2U8VXNlcj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPFVzZXI+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnB1dChgL2FkbWluL3VzZXJzLyR7aWR9L3ZlcmlmeS1waG9uZWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgZGVsZXRlVXNlcihpZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgdGhpcy5jbGllbnQuZGVsZXRlKGAvYWRtaW4vdXNlcnMvJHtpZH1gKVxuICB9XG5cbiAgLy8gQWRtaW4gRGlzY291bnQgQ29kZSBNYW5hZ2VtZW50XG4gIGFzeW5jIGdldERpc2NvdW50Q29kZXMocGFnZTogbnVtYmVyID0gMCwgbGltaXQ6IG51bWJlciA9IDEwLCBzZWFyY2g/OiBzdHJpbmcpOiBQcm9taXNlPFBhZ2luYXRlZFJlc3BvbnNlPERpc2NvdW50Q29kZT4+IHtcbiAgICBsZXQgdXJsID0gYC9hZG1pbi9kaXNjb3VudC1jb2Rlcz9wYWdlPSR7cGFnZX0mbGltaXQ9JHtsaW1pdH1gXG4gICAgaWYgKHNlYXJjaCkgdXJsICs9IGAmc2VhcmNoPSR7c2VhcmNofWBcblxuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPFBhZ2luYXRlZFJlc3BvbnNlPERpc2NvdW50Q29kZT4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KHVybClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9XG5cbiAgYXN5bmMgZ2V0RGlzY291bnRDb2RlKGlkOiBzdHJpbmcpOiBQcm9taXNlPERpc2NvdW50Q29kZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPERpc2NvdW50Q29kZT4+ID0gYXdhaXQgdGhpcy5jbGllbnQuZ2V0KGAvYWRtaW4vZGlzY291bnQtY29kZXMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGNyZWF0ZURpc2NvdW50Q29kZShkaXNjb3VudENvZGVEYXRhOiBDcmVhdGVEaXNjb3VudENvZGVSZXF1ZXN0KTogUHJvbWlzZTxEaXNjb3VudENvZGU+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxEaXNjb3VudENvZGU+PiA9IGF3YWl0IHRoaXMuY2xpZW50LnBvc3QoJy9hZG1pbi9kaXNjb3VudC1jb2RlcycsIGRpc2NvdW50Q29kZURhdGEpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YVxuICB9XG5cbiAgYXN5bmMgdXBkYXRlRGlzY291bnRDb2RlKGlkOiBzdHJpbmcsIGRpc2NvdW50Q29kZURhdGE6IENyZWF0ZURpc2NvdW50Q29kZVJlcXVlc3QpOiBQcm9taXNlPERpc2NvdW50Q29kZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPERpc2NvdW50Q29kZT4+ID0gYXdhaXQgdGhpcy5jbGllbnQucHV0KGAvYWRtaW4vZGlzY291bnQtY29kZXMvJHtpZH1gLCBkaXNjb3VudENvZGVEYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGRlbGV0ZURpc2NvdW50Q29kZShpZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgYXdhaXQgdGhpcy5jbGllbnQuZGVsZXRlKGAvYWRtaW4vZGlzY291bnQtY29kZXMvJHtpZH1gKVxuICB9XG5cbiAgYXN5bmMgdG9nZ2xlRGlzY291bnRDb2RlU3RhdHVzKGlkOiBzdHJpbmcpOiBQcm9taXNlPERpc2NvdW50Q29kZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPERpc2NvdW50Q29kZT4+ID0gYXdhaXQgdGhpcy5jbGllbnQucG9zdChgL2FkbWluL2Rpc2NvdW50LWNvZGVzLyR7aWR9L3RvZ2dsZS1zdGF0dXNgKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIGFwcGx5RGlzY291bnRDb2RlKHJlcXVlc3Q6IEFwcGx5RGlzY291bnRSZXF1ZXN0KTogUHJvbWlzZTxEaXNjb3VudFJlc3VsdD4ge1xuICAgIGNvbnN0IHJlc3BvbnNlOiBBeGlvc1Jlc3BvbnNlPEFwaVJlc3BvbnNlPERpc2NvdW50UmVzdWx0Pj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wb3N0KCcvZGlzY291bnQtY29kZXMvYXBwbHknLCByZXF1ZXN0KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxuXG4gIGFzeW5jIHZhbGlkYXRlRGlzY291bnRDb2RlKGNvZGU6IHN0cmluZyk6IFByb21pc2U8RGlzY291bnRDb2RlPiB7XG4gICAgY29uc3QgcmVzcG9uc2U6IEF4aW9zUmVzcG9uc2U8QXBpUmVzcG9uc2U8RGlzY291bnRDb2RlPj4gPSBhd2FpdCB0aGlzLmNsaWVudC5nZXQoYC9kaXNjb3VudC1jb2Rlcy92YWxpZGF0ZS8ke2NvZGV9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gIH1cblxuICAvLyBBZG1pbiBQcm9kdWN0IEZlYXR1cmVkIE1hbmFnZW1lbnRcbiAgYXN5bmMgdG9nZ2xlUHJvZHVjdEZlYXR1cmVkU3RhdHVzKGlkOiBzdHJpbmcpOiBQcm9taXNlPFByb2R1Y3Q+IHtcbiAgICBjb25zdCByZXNwb25zZTogQXhpb3NSZXNwb25zZTxBcGlSZXNwb25zZTxQcm9kdWN0Pj4gPSBhd2FpdCB0aGlzLmNsaWVudC5wdXQoYC9hZG1pbi9wcm9kdWN0cy8ke2lkfS90b2dnbGUtZmVhdHVyZWRgKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgYXBpQ2xpZW50ID0gbmV3IEFwaUNsaWVudCgpXG5leHBvcnQgZGVmYXVsdCBhcGlDbGllbnRcbiJdLCJuYW1lcyI6WyJheGlvcyIsIkFwaUNsaWVudCIsImxvZ2luIiwiY3JlZGVudGlhbHMiLCJyZXNwb25zZSIsImNsaWVudCIsInBvc3QiLCJkYXRhIiwiZ2V0RGFzaGJvYXJkT3ZlcnZpZXciLCJnZXQiLCJnZXRNb250aGx5U2FsZXNEYXRhIiwibW9udGhzIiwiZ2V0V2Vla2x5U2FsZXNEYXRhIiwid2Vla3MiLCJnZXRNb3N0U29sZFByb2R1Y3RzIiwiZGF5cyIsImxpbWl0IiwiZ2V0UG9wdWxhckNhdGVnb3JpZXMiLCJnZXRVc2VyRW5nYWdlbWVudE1ldHJpY3MiLCJnZXRPcmRlcnMiLCJwYWdlIiwic2VhcmNoIiwic3RhdHVzIiwiYmFja2VuZFBhZ2UiLCJNYXRoIiwibWF4IiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwidG9TdHJpbmciLCJ0cmltIiwiYXBwZW5kIiwiZ2V0T3JkZXIiLCJpZCIsInVwZGF0ZU9yZGVyU3RhdHVzIiwibm90ZXMiLCJwdXQiLCJnZXRVc2VycyIsImdldFVzZXIiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiLCJnZXRQcm9kdWN0cyIsImNhdGVnb3J5SWQiLCJ1cmwiLCJnZXRQcm9kdWN0IiwiZ2V0Q2F0ZWdvcmllcyIsImdldEJhbm5lcnMiLCJnZXRGZWF0dXJlZFByb2R1Y3RzIiwic2VuZE5vdGlmaWNhdGlvbiIsInJlcXVlc3QiLCJnZXROb3RpZmljYXRpb25IaXN0b3J5Iiwic2l6ZSIsInVzZXJJZCIsInVwbG9hZEltYWdlIiwiZmlsZSIsInR5cGUiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiaGVhZGVycyIsImNyZWF0ZVByb2R1Y3QiLCJwcm9kdWN0RGF0YSIsInVwZGF0ZVByb2R1Y3QiLCJkZWxldGVQcm9kdWN0IiwiZGVsZXRlIiwiZ2V0QWRtaW5DYXRlZ29yaWVzIiwiY3JlYXRlQ2F0ZWdvcnkiLCJjYXRlZ29yeURhdGEiLCJ1cGRhdGVDYXRlZ29yeSIsImRlbGV0ZUNhdGVnb3J5IiwiZ2V0QmFubmVyIiwiY3JlYXRlQmFubmVyIiwiYmFubmVyRGF0YSIsInVwZGF0ZUJhbm5lciIsImRlbGV0ZUJhbm5lciIsInRvZ2dsZUJhbm5lclN0YXR1cyIsImdldEFkbWluVXNlcnMiLCJ0b2dnbGVVc2VyU3RhdHVzIiwidmVyaWZ5VXNlckVtYWlsIiwidmVyaWZ5VXNlclBob25lIiwiZGVsZXRlVXNlciIsImdldERpc2NvdW50Q29kZXMiLCJnZXREaXNjb3VudENvZGUiLCJjcmVhdGVEaXNjb3VudENvZGUiLCJkaXNjb3VudENvZGVEYXRhIiwidXBkYXRlRGlzY291bnRDb2RlIiwiZGVsZXRlRGlzY291bnRDb2RlIiwidG9nZ2xlRGlzY291bnRDb2RlU3RhdHVzIiwiYXBwbHlEaXNjb3VudENvZGUiLCJ2YWxpZGF0ZURpc2NvdW50Q29kZSIsImNvZGUiLCJ0b2dnbGVQcm9kdWN0RmVhdHVyZWRTdGF0dXMiLCJjb25zdHJ1Y3RvciIsImNyZWF0ZSIsImJhc2VVUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsInRpbWVvdXQiLCJpbnRlcmNlcHRvcnMiLCJ1c2UiLCJjb25maWciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJBdXRob3JpemF0aW9uIiwiZXJyb3IiLCJQcm9taXNlIiwicmVqZWN0IiwicmVtb3ZlSXRlbSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImFwaUNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});