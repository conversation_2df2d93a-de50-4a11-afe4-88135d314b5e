package com.grocease.service;

import com.grocease.entity.*;
import com.grocease.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final ProductRepository productRepository;
    private final AddressRepository addressRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        if (orderRepository.count() == 0) {
            log.info("No orders found, creating sample orders for testing...");
            createSampleOrders();
        }
    }

    private void createSampleOrders() {
        try {
            // Create a test user if not exists
            User testUser = userRepository.findByEmail("<EMAIL>")
                    .orElseGet(() -> createTestUser());

            // Create a test address if not exists
            Address testAddress = addressRepository.findByUserIdAndIsDefaultTrue(testUser.getId())
                    .orElseGet(() -> createTestAddress(testUser));

            // Get some products
            List<Product> products = productRepository.findAll();
            if (products.isEmpty()) {
                log.warn("No products found, cannot create sample orders");
                return;
            }

            // Create sample orders with different statuses
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.PENDING, "ORD-001");
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.CONFIRMED, "ORD-002");
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.PREPARING, "ORD-003");
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.OUT_FOR_DELIVERY, "ORD-004");
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.DELIVERED, "ORD-005");
            createSampleOrder(testUser, testAddress, products, Order.OrderStatus.CANCELLED, "ORD-006");

            log.info("Sample orders created successfully");
        } catch (Exception e) {
            log.error("Error creating sample orders: {}", e.getMessage(), e);
        }
    }

    private User createTestUser() {
        User user = User.builder()
                .name("Test User")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .phone("1234567890")
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.USER)
                .build();
        return userRepository.save(user);
    }

    private Address createTestAddress(User user) {
        Address address = Address.builder()
                .type(Address.AddressType.HOME)
                .street("123 Test Street")
                .city("Test City")
                .state("Test State")
                .zipCode("12345")
                .isDefault(true)
                .user(user)
                .build();
        return addressRepository.save(address);
    }

    private void createSampleOrder(User user, Address address, List<Product> products, 
                                 Order.OrderStatus status, String orderNumber) {
        // Create order
        Order order = Order.builder()
                .orderNumber(orderNumber)
                .total(BigDecimal.valueOf(45.50))
                .subtotal(BigDecimal.valueOf(40.00))
                .deliveryFee(BigDecimal.valueOf(5.50))
                .discount(BigDecimal.ZERO)
                .status(status)
                .estimatedDelivery(LocalDateTime.now().plusHours(2))
                .user(user)
                .deliveryAddress(address)
                .items(new ArrayList<>())
                .build();

        if (status == Order.OrderStatus.DELIVERED) {
            order.setDeliveredAt(LocalDateTime.now().minusHours(1));
        }

        Order savedOrder = orderRepository.save(order);

        // Add some order items
        if (!products.isEmpty()) {
            Product product1 = products.get(0);
            OrderItem item1 = OrderItem.builder()
                    .order(savedOrder)
                    .product(product1)
                    .quantity(2)
                    .unitPrice(product1.getPrice())
                    .totalPrice(product1.getPrice().multiply(BigDecimal.valueOf(2)))
                    .build();

            if (products.size() > 1) {
                Product product2 = products.get(1);
                OrderItem item2 = OrderItem.builder()
                        .order(savedOrder)
                        .product(product2)
                        .quantity(1)
                        .unitPrice(product2.getPrice())
                        .totalPrice(product2.getPrice())
                        .build();
                savedOrder.getItems().add(item2);
            }

            savedOrder.getItems().add(item1);
            orderRepository.save(savedOrder);
        }

        log.info("Created sample order: {} with status: {}", orderNumber, status);
    }
}
