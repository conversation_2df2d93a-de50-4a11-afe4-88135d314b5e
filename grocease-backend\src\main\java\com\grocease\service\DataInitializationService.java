package com.grocease.service;

import com.grocease.entity.*;
import com.grocease.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final ProductRepository productRepository;
    private final AddressRepository addressRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        // Create sample users if none exist
        if (userRepository.count() == 0) {
            log.info("No users found, creating sample users for testing...");
            createSampleUsers();
        }

        // Create sample orders if none exist
        if (orderRepository.count() == 0) {
            log.info("No orders found, creating sample orders for testing...");
            createSampleOrders();
        }
    }

    private void createSampleUsers() {
        try {
            // Create sample users for testing
            createSampleUser("John Doe", "<EMAIL>", "password123", "1234567890");
            createSampleUser("Jane Smith", "<EMAIL>", "password123", "0987654321");
            createSampleUser("Bob Johnson", "<EMAIL>", "password123", "5555555555");

            // Create admin user
            createAdminUser("Admin User", "<EMAIL>", "admin123", "1111111111");

            log.info("Sample users created successfully");
        } catch (Exception e) {
            log.error("Error creating sample users: {}", e.getMessage(), e);
        }
    }

    private void createSampleUser(String name, String email, String password, String phone) {
        User user = User.builder()
                .name(name)
                .email(email)
                .password(passwordEncoder.encode(password))
                .phone(phone)
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.USER)
                .build();
        userRepository.save(user);
        log.info("Created sample user: {}", email);
    }

    private void createAdminUser(String name, String email, String password, String phone) {
        User user = User.builder()
                .name(name)
                .email(email)
                .password(passwordEncoder.encode(password))
                .phone(phone)
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.ADMIN)
                .build();
        userRepository.save(user);
        log.info("Created admin user: {}", email);
    }

    private void createSampleOrders() {
        try {
            // Get some products first
            List<Product> products = productRepository.findAll();
            if (products.isEmpty()) {
                log.warn("No products found, cannot create sample orders");
                return;
            }

            // Get all existing users and create orders for them
            List<User> existingUsers = userRepository.findAll();
            if (!existingUsers.isEmpty()) {
                log.info("Found {} existing users, creating orders for them", existingUsers.size());
                for (User user : existingUsers) {
                    if (user.getRole() == User.Role.USER) { // Only create orders for regular users, not admins
                        createOrdersForUser(user, products);
                    }
                }
            }

            // Also create a test user with orders
            User testUser = userRepository.findByEmail("<EMAIL>")
                    .orElseGet(() -> createTestUser());
            createOrdersForUser(testUser, products);

            log.info("Sample orders created successfully");
        } catch (Exception e) {
            log.error("Error creating sample orders: {}", e.getMessage(), e);
        }
    }

    private void createOrdersForUser(User user, List<Product> products) {
        // Create a test address if not exists
        Address userAddress = addressRepository.findByUserIdAndIsDefaultTrue(user.getId())
                .orElseGet(() -> createTestAddress(user));

        // Create sample orders with different statuses
        String userPrefix = user.getEmail().split("@")[0].toUpperCase();
        createSampleOrder(user, userAddress, products, Order.OrderStatus.PENDING, userPrefix + "-001");
        createSampleOrder(user, userAddress, products, Order.OrderStatus.CONFIRMED, userPrefix + "-002");
        createSampleOrder(user, userAddress, products, Order.OrderStatus.PREPARING, userPrefix + "-003");
        createSampleOrder(user, userAddress, products, Order.OrderStatus.OUT_FOR_DELIVERY, userPrefix + "-004");
        createSampleOrder(user, userAddress, products, Order.OrderStatus.DELIVERED, userPrefix + "-005");
        createSampleOrder(user, userAddress, products, Order.OrderStatus.CANCELLED, userPrefix + "-006");

        log.info("Created sample orders for user: {}", user.getEmail());
    }

    private User createTestUser() {
        User user = User.builder()
                .name("Test User")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .phone("1234567890")
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.USER)
                .build();
        return userRepository.save(user);
    }

    private Address createTestAddress(User user) {
        Address address = Address.builder()
                .type(Address.AddressType.HOME)
                .street("123 Test Street")
                .city("Test City")
                .state("Test State")
                .zipCode("12345")
                .isDefault(true)
                .user(user)
                .build();
        return addressRepository.save(address);
    }

    private void createSampleOrder(User user, Address address, List<Product> products, 
                                 Order.OrderStatus status, String orderNumber) {
        // Create order
        Order order = Order.builder()
                .orderNumber(orderNumber)
                .total(BigDecimal.valueOf(45.50))
                .subtotal(BigDecimal.valueOf(40.00))
                .deliveryFee(BigDecimal.valueOf(5.50))
                .discount(BigDecimal.ZERO)
                .status(status)
                .estimatedDelivery(LocalDateTime.now().plusHours(2))
                .user(user)
                .deliveryAddress(address)
                .items(new ArrayList<>())
                .build();

        if (status == Order.OrderStatus.DELIVERED) {
            order.setDeliveredAt(LocalDateTime.now().minusHours(1));
        }

        Order savedOrder = orderRepository.save(order);

        // Add some order items
        if (!products.isEmpty()) {
            Product product1 = products.get(0);
            OrderItem item1 = OrderItem.builder()
                    .order(savedOrder)
                    .product(product1)
                    .quantity(2)
                    .unitPrice(product1.getPrice())
                    .totalPrice(product1.getPrice().multiply(BigDecimal.valueOf(2)))
                    .build();

            if (products.size() > 1) {
                Product product2 = products.get(1);
                OrderItem item2 = OrderItem.builder()
                        .order(savedOrder)
                        .product(product2)
                        .quantity(1)
                        .unitPrice(product2.getPrice())
                        .totalPrice(product2.getPrice())
                        .build();
                savedOrder.getItems().add(item2);
            }

            savedOrder.getItems().add(item1);
            orderRepository.save(savedOrder);
        }

        log.info("Created sample order: {} with status: {}", orderNumber, status);
    }
}
