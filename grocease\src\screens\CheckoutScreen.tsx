import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Ionicons } from '@expo/vector-icons';

import { api } from '../services/api';
import { useCart } from '../hooks/useCart';
import { formatPrice, validateEmail, validatePhone } from '../utils';
import { Address } from '../types';

import Button from '../components/Button';
import LoadingSpinner from '../components/LoadingSpinner';

interface CheckoutForm {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  notes: string;
}

const CheckoutScreen = () => {
  const navigation = useNavigation();
  const { items, getCartSummary, clearCart } = useCart();
  const cartSummary = getCartSummary();

  const [form, setForm] = useState<CheckoutForm>({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    notes: ''
  });

  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);

  // Fetch user data for pre-filling
  const { data: userData } = useQuery({
    queryKey: ['user'],
    queryFn: api.getUser,
  });

  // Fetch user addresses
  const { data: addressesData } = useQuery({
    queryKey: ['addresses'],
    queryFn: api.getUserAddresses,
  });

  // Create address mutation
  const createAddressMutation = useMutation({
    mutationFn: api.createAddress,
    onError: (error) => {
      Alert.alert(
        'Address Error',
        'Failed to create delivery address. Please try again.',
        [{ text: 'OK' }]
      );
    }
  });

  // Create order mutation
  const createOrderMutation = useMutation({
    mutationFn: api.createOrder,
    onSuccess: (response) => {
      clearCart();
      navigation.navigate('OrderConfirmation' as never, {
        orderId: response.data.id
      } as never);
    },
    onError: (error: any) => {
      console.error('Order creation error:', error);
      Alert.alert(
        'Order Failed',
        error?.message || 'Something went wrong while placing your order. Please try again.',
        [{ text: 'OK' }]
      );
    }
  });

  React.useEffect(() => {
    if (userData?.data) {
      const user = userData.data;
      setForm(prev => ({
        ...prev,
        name: user.name,
        email: user.email,
        phone: user.phone
      }));

      // Set default address if available
      const defaultAddress = user.addresses.find(addr => addr.isDefault);
      if (defaultAddress) {
        setSelectedAddress(defaultAddress);
        setForm(prev => ({
          ...prev,
          address: defaultAddress.street,
          city: defaultAddress.city,
          state: defaultAddress.state,
          zipCode: defaultAddress.zipCode
        }));
      }
    }
  }, [userData]);

  const updateForm = (field: keyof CheckoutForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!form.name.trim()) {
      Alert.alert('Validation Error', 'Please enter your name');
      return false;
    }
    if (!validateEmail(form.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return false;
    }
    if (!validatePhone(form.phone)) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return false;
    }

    // Only validate address fields if no saved address is selected
    if (!selectedAddress || selectedAddress.id === 'new') {
      if (!form.address.trim()) {
        Alert.alert('Validation Error', 'Please enter your address');
        return false;
      }
      if (!form.city.trim()) {
        Alert.alert('Validation Error', 'Please enter your city');
        return false;
      }
      if (!form.state.trim()) {
        Alert.alert('Validation Error', 'Please enter your state');
        return false;
      }
      if (!form.zipCode.trim()) {
        Alert.alert('Validation Error', 'Please enter your zip code');
        return false;
      }
    } else if (!selectedAddress) {
      Alert.alert('Validation Error', 'Please select a delivery address');
      return false;
    }

    return true;
  };

  const handlePlaceOrder = async () => {
    if (!validateForm()) return;

    try {
      let addressId: number;

      // If using existing address
      if (selectedAddress && selectedAddress.id !== 'new') {
        addressId = parseInt(selectedAddress.id);
      } else {
        // Create new address first
        const addressResponse = await createAddressMutation.mutateAsync({
          type: 'OTHER',
          street: form.address,
          city: form.city,
          state: form.state,
          zipCode: form.zipCode,
          isDefault: false
        });

        if (!addressResponse.success || !addressResponse.data) {
          throw new Error('Failed to create delivery address');
        }

        addressId = parseInt(addressResponse.data.id);
      }

      // Transform cart items to the format expected by backend
      const orderItems = items.map(item => ({
        productId: parseInt(item.product.id),
        quantity: item.quantity
      }));

      // Create order with proper data structure
      const orderData: CreateOrderRequest = {
        items: orderItems,
        deliveryAddressId: addressId,
        total: Math.round(cartSummary.total),
        subtotal: Math.round(cartSummary.subtotal),
        deliveryFee: Math.round(cartSummary.deliveryFee),
        discount: 0
      };

      createOrderMutation.mutate(orderData);
    } catch (error) {
      console.error('Order placement error:', error);
      Alert.alert(
        'Order Failed',
        error instanceof Error ? error.message : 'Failed to place order. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const selectSavedAddress = (address: Address) => {
    setSelectedAddress(address);
    setForm(prev => ({
      ...prev,
      address: address.street,
      city: address.city,
      state: address.state,
      zipCode: address.zipCode
    }));
  };

  const renderSavedAddresses = () => {
    const addresses = addressesData?.data || [];

    return (
      <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
        <Text className="text-xl font-bold text-neutral-800 mb-4">
          Delivery Address
        </Text>

        {addresses.length > 0 && (
          <>
            <Text className="text-sm text-neutral-600 mb-3">
              Choose from saved addresses:
            </Text>
            {addresses.map((address) => (
              <TouchableOpacity
                key={address.id}
                className={`p-4 rounded-xl border-2 mb-3 ${
                  selectedAddress?.id === address.id
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-neutral-200 bg-neutral-50'
                }`}
                onPress={() => selectSavedAddress(address)}
              >
                <View className="flex-row items-center justify-between">
                  <View className="flex-1">
                    <Text className="font-semibold text-neutral-800 mb-1">
                      {address.type.charAt(0) + address.type.slice(1).toLowerCase()}
                      {address.isDefault && ' (Default)'}
                    </Text>
                    <Text className="text-neutral-600 text-sm">
                      {address.street}
                    </Text>
                    <Text className="text-neutral-600 text-sm">
                      {address.city}, {address.state} {address.zipCode}
                    </Text>
                  </View>
                  {selectedAddress?.id === address.id && (
                    <Ionicons name="checkmark-circle" size={24} color="#22c55e" />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </>
        )}

        {addresses.length === 0 && (
          <Text className="text-sm text-neutral-600 mb-3">
            No saved addresses found. Add a new address below:
          </Text>
        )}

        <TouchableOpacity
          className={`p-4 rounded-xl border-2 border-dashed mb-2 ${
            !selectedAddress || selectedAddress.id === 'new'
              ? 'border-primary-300 bg-primary-50'
              : 'border-neutral-300 bg-neutral-50'
          }`}
          onPress={() => setSelectedAddress(null)}
        >
          <View className="flex-row items-center justify-center">
            <Ionicons name="add" size={20} color={!selectedAddress || selectedAddress.id === 'new' ? '#22c55e' : '#64748b'} />
            <Text className={`ml-2 font-medium ${
              !selectedAddress || selectedAddress.id === 'new' ? 'text-primary-700' : 'text-neutral-600'
            }`}>
              Add New Address
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderFormField = (
    label: string,
    field: keyof CheckoutForm,
    placeholder: string,
    keyboardType: 'default' | 'email-address' | 'phone-pad' = 'default',
    multiline: boolean = false
  ) => (
    <View className="mb-4">
      <Text className="text-base font-semibold text-neutral-800 mb-2">
        {label}
      </Text>
      <TextInput
        className={`bg-neutral-100 rounded-lg px-4 py-3 text-base text-neutral-800 ${
          multiline ? 'h-20' : ''
        }`}
        placeholder={placeholder}
        placeholderTextColor="#94a3b8"
        value={form[field]}
        onChangeText={(value) => updateForm(field, value)}
        keyboardType={keyboardType}
        multiline={multiline}
        textAlignVertical={multiline ? 'top' : 'center'}
      />
    </View>
  );

  if (items.length === 0) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="bag-outline" size={80} color="#cbd5e1" />
          <Text className="text-2xl font-bold text-neutral-800 mt-6 mb-2">
            Your cart is empty
          </Text>
          <Text className="text-base text-neutral-600 text-center mb-8">
            Add some items to your cart before checking out.
          </Text>
          <Button
            title="Start Shopping"
            onPress={() => navigation.navigate('Main' as never)}
            size="lg"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-neutral-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-4 py-6">
          {/* Contact Information */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-xl font-bold text-neutral-800 mb-4">
              Contact Information
            </Text>
            {renderFormField('Full Name', 'name', 'Enter your full name')}
            {renderFormField('Email', 'email', 'Enter your email address', 'email-address')}
            {renderFormField('Phone', 'phone', 'Enter your phone number', 'phone-pad')}
          </View>

          {/* Saved Addresses */}
          {renderSavedAddresses()}

          {/* Delivery Address - Only show if no saved address selected or adding new */}
          {(!selectedAddress || selectedAddress.id === 'new') && (
            <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
              <Text className="text-xl font-bold text-neutral-800 mb-4">
                Delivery Address
              </Text>
              {renderFormField('Street Address', 'address', 'Enter your street address')}
              <View className="flex-row space-x-3">
                <View className="flex-1">
                  {renderFormField('City', 'city', 'City')}
                </View>
                <View className="w-20">
                  {renderFormField('State', 'state', 'State')}
                </View>
              </View>
              {renderFormField('Zip Code', 'zipCode', 'Zip Code')}
              {renderFormField('Delivery Notes (Optional)', 'notes', 'Any special instructions...', 'default', true)}
            </View>
          )}

          {/* Order Summary */}
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-neutral-100 mb-6">
            <Text className="text-xl font-bold text-neutral-800 mb-4">
              Order Summary
            </Text>

            <View className="space-y-3">
              <View className="flex-row justify-between">
                <Text className="text-base text-neutral-700">
                  Subtotal ({cartSummary.itemCount} items)
                </Text>
                <Text className="text-base font-semibold text-neutral-800">
                  {formatPrice(cartSummary.subtotal)}
                </Text>
              </View>

              <View className="flex-row justify-between">
                <Text className="text-base text-neutral-700">
                  Delivery Fee
                </Text>
                <Text className={`text-base font-semibold ${
                  cartSummary.deliveryFee === 0 ? 'text-primary-600' : 'text-neutral-800'
                }`}>
                  {cartSummary.deliveryFee === 0 ? 'FREE' : formatPrice(cartSummary.deliveryFee)}
                </Text>
              </View>

              <View className="border-t border-neutral-200 pt-3">
                <View className="flex-row justify-between">
                  <Text className="text-lg font-bold text-neutral-800">
                    Total
                  </Text>
                  <Text className="text-lg font-bold text-neutral-800">
                    {formatPrice(cartSummary.total)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View className="bg-white border-t border-neutral-200 px-4 py-4">
        <Button
          title={`Place Order • ${formatPrice(cartSummary.total)}`}
          onPress={handlePlaceOrder}
          loading={createOrderMutation.isPending || createAddressMutation.isPending}
          size="lg"
          fullWidth
        />
      </View>
    </SafeAreaView>
  );
};

export default CheckoutScreen;
